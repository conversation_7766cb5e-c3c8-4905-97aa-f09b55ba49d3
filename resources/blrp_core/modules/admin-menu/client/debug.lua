local debug_active = false
local debugThreadRunning = false
local objindex = {}

tAdmin.toggleDebug = function()
  debug_active = not debug_active

  if debug_active then
    startDebugThread()
  end
end

function startDebugThread()
  if not debug_thread_active then
    debug_thread_active = true
    Citizen.CreateThread(function()
      while debug_active do
        Citizen.Wait(0)
					local playerped = PlayerPedId()
					local playerpedPos = GetEntityCoords(playerped, nil)
					for entity, alive in pairs(objindex) do
            local entityPos = GetEntityCoords(entity)
            if #(vector3(entityPos.x,entityPos.y,entityPos.z)-vector3(playerpedPos.x,playerpedPos.y,playerpedPos.z)) < 2.2001 then
							local posx,posy,posz = table.unpack(GetEntityCoords(entity, nil))
							tAdmin.drawText3Ds(GetEntityModel(entity),posx,posy,posz+1.0)
						end
					end
      end
      debug_thread_active = false
    end)

		Citizen.CreateThread(function()
      while debug_active do
				Citizen.Wait(2000)
				PopulateObjectIndex()
      end
      debug_thread_active = false
    end)
  end
end

local pool_stats_active = false
local pool_stats_thread_active = false

tAdmin.togglePoolStats = function()
  pool_stats_active = not pool_stats_active

  if pool_stats_active then
    startPoolStatsThread()
  end

  return pool_stats_active
end

function startPoolStatsThread()
  if pool_stats_thread_active then
    return
  end

  Citizen.CreateThread(function()
    while pool_stats_active do
      local pool_stats = {}
      local ped_coords = GetEntityCoords(PlayerPedId())

      print('----------------------------------------------------------------------')
      print('Game pool statistics')
      print('Unix time', GlobalState.server_time)
      print('-----------------------------------')

      for _, pool in pairs({ 'CObject', 'CPed', 'CVehicle' }) do
        pool_stats[pool] = { count = 0, networked = 0 }

        print(pool, 'Starting processing for pool')

        for _, entity in pairs(GetGamePool(pool)) do
          local networked = NetworkGetEntityIsNetworked(entity)

          pool_stats[pool].count = pool_stats[pool].count + 1

          if networked then
            pool_stats[pool].networked = pool_stats[pool].networked + 1

            local distance = #(ped_coords - GetEntityCoords(entity))
            local coords = GetEntityCoords(entity)
            local population_type = ({
              'POPTYPE_UNKNOWN',
              'POPTYPE_RANDOM_PERMANENT',
              'POPTYPE_RANDOM_PARKED',
              'POPTYPE_RANDOM_PATROL',
              'POPTYPE_RANDOM_SCENARIO',
              'POPTYPE_RANDOM_AMBIENT',
              'POPTYPE_PERMANENT',
              'POPTYPE_MISSION',
              'POPTYPE_REPLAY',
              'POPTYPE_CACHE',
              'POPTYPE_TOOL',
            })[(GetEntityPopulationType(entity) + 1)] or 'POPTYPE_UNKNOWN (' .. GetEntityPopulationType(entity) .. ')'

            print(pool, entity, population_type, GetEntityArchetypeName(entity), coords, distance)
          end
        end

        print(pool, 'Finished processing for pool', 'Count:', pool_stats[pool].count, 'Networked:', pool_stats[pool].networked)
      end

      print('----------------------------------------------------------------------')

      Citizen.Wait(10000)
    end
  end)
end

function PopulateObjectIndex()
  local handle, obj = FindFirstObject()
  local finished = false
  repeat
    if not IsEntityDead(obj) then
    	objindex[obj] = true
    end
    finished, obj = FindNextObject(handle)
  until not finished
  EndFindObject(handle)
end

core.event('core:client:teleports:requestFromTarget', function(_, target_data)
  if not target_data or not target_data.destination then
    return
  end
  if target_data.elevator_delay and not (target_data.ems_bypass and exports.blrp_core:me().hasGroup('LSFD')) then
    DoScreenFadeOut(5000) 
    TriggerServerEvent('InteractSound_SV:PlayWithinDistance', 15.0, 'elevator-ding', 0.1)
    while IsScreenFadingOut() do Citizen.Wait(0) end
    TriggerEvent('core:client:teleports:request', target_data.destination)
    DoScreenFadeIn(1000)
  else
    TriggerEvent('core:client:teleports:request', target_data.destination)
  end
end)

core.event('core:client:teleports:request', function(destination_code)
  if core.me().isHandcuffed() or core.me().isZipTied() then
    return
  end

  local destination = teleports_config.destinations[destination_code]

  if not destination then
    return
  end

  tSurvival.teleport(destination.x, destination.y, destination.z, destination.w)
end)

core.event('core:client:teleports:requestFromTargetHouse', function(_, target_data)
  if not target_data or not target_data.destination then
    return
  end
  if target_data.door_delay and not (target_data.ems_bypass and exports.blrp_core:me().hasGroup('LSFD')) then
    DoScreenFadeOut(2000) 
    TriggerServerEvent('InteractSound_SV:PlayWithinDistance', 15.0, 'door-opening-closing', 1.0)
    while IsScreenFadingOut() do Citizen.Wait(0) end
    TriggerEvent('core:client:teleports:request', target_data.destination)
    DoScreenFadeIn(1000)
  else
    TriggerEvent('core:client:teleports:request', target_data.destination)
  end
end)

@font-face {
  font-family: 'nk57mono-no-rg';
  src: url('nk57-monospace-no-rg.ttf');
}

/** {*/
/*    box-sizing: border-box;*/
/*    -webkit-user-select: none;*/
/*    user-select: none;*/
/*}*/

/*p {*/
/*    -webkit-user-select: text;*/
/*    -moz-user-select: text;*/
/*    user-select: text;*/
/*}*/

*, *::after, *::before {
  user-select: none;
  -webkit-user-select: none;
  -webkit-user-drag: none;
  -webkit-app-region: no-drag;
}

/* width */
::-webkit-scrollbar {
    width: 4px;
    display: none;
}

/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
    display: none;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
    display: none;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
    display: none;
}

body {
    font-family: 'IBM Plex Serif', serif;
    background: transparent;
    /*display: none;*/
}

.content {
    display: none;
}

.iziToast-wrapper {
    pointer-events: all !important;
    margin-top: 320px;
    z-index: 99999;
    max-width: 410px;
    font-family: 'IBM Plex Serif', serif;
    max-height: 600px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.iziToast-title {
    display: block !important;
}

.iziToast-texts {
    margin-top: 0 !important;
}

.iziToast-texts {
    margin-bottom: 0 !important;
}

.iziToast-buttons {
    float: right !important
}

.message-contents {
    display: block;
    margin-left: 7px;
}

.col-sm {
    max-height: 730px;
    margin: 15px;
}

.must-scroll {
    overflow-y: scroll;
}

.section {
    background-color: black;
    border: .5px solid rgba(128, 128, 128, 0.51);
    margin: 5px;
    padding: 5px;
}

.section-title {
    margin-right: 6px;
}

.command {
    margin-left: 10px;
    margin-top: 2px;
    margin-bottom: 2px;
}

.command-args {
    font-size: 12px;
    /*color: rgb(115, 115, 115);*/
}

.command-info {
    display: block;
    font-size: 11px;
}

.hotkey {
    margin-top: 12px;
    margin-bottom: 12px;
}

.hotkey-info {
    font-size: 12px;
}

.active-dispatch {
  display: flex;
  flex-direction: column;
  right: 0;
  text-align: left;
  letter-spacing: 0.75px;
  position: relative;
  max-width: 100vw;
}

.dispatch-list-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  overflow-x: auto;
  max-height: 820px;
}

.dispatch-column {
  flex: 0 0 auto;
  width: 400px;
  display: flex;
  flex-direction: column;
}

.dispatch-person {
  min-width: 215px;
  max-width: 215px;
}

.dispatch-profile-item {
  cursor: pointer;
}

.dispatch-profile-item.dispatch-hovered {
  background-color: #2a2a2a !important;
  background-image: none !important;
}

.dispatch-buttons {
  min-width: 110px;
  max-width: 110px;
}

.dispatch-status {
  min-width: 365px;
  max-width: 365px;
}

.dispatch-outline-dispatcher {
  box-shadow: inset 0 0 0 2px #c8ff00;
}

.dispatch-outline-training {
  box-shadow: inset 0 0 0 2px #663399;
}

.dispatch-outline-busy {
  box-shadow: inset 0 0 0 2px #ff8c00;
}

.dispatch-outline-s13 {
  box-shadow: inset 0 0 0 2px #db3545;
}

.dispatch-status span {
  display: inline-block;
  text-transform: uppercase;
  font-size: 11px;
  font-weight: bold;
  margin-right: 3px;
}

.dispatch-profile {
    display: flex;
    /* justify-content: space-between; */
    font-family: 'nk57mono-no-rg' !important;
    margin: 2px 5px 2px 0;
    /*border-left: 6px solid #242424;*/
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.7) !important;
    color: white;
    /* width: 300px; */
    /*border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;*/
    user-select: none;
    width: fit-content;
}

.dispatch-bg-doc {
    background-color: rgba(66, 36, 0, 0.7) !important;
}

.dispatch-bg-training {
    background-color: rgba(102, 51, 153, 0.7) !important;
}

.dispatch-bg-ems2 {
    background-color: rgba(249, 138, 138, 0.7) !important;
}

.dispatch-bg-emstmu {
    background-color: rgba(205, 63, 152, 0.7) !important;
}

.dispatch-bg-dispatcher {
    background-color: rgba(255, 169, 61, 0.7) !important;
}

.dispatch-bg-ranger {
    background-color: rgba(93, 124, 79, 0.7) !important;
}

.dispatch-bg-rrtow {
    background: rgba(255, 106, 0, 0.8) !important;
    background: linear-gradient(90deg,rgba(255, 106, 0, 0.8) 0%, rgba(0, 0, 0, 0.7) 20%) !important;
}

.dispatch-bg-busy {
    background-color: rgba(66, 0, 0, 0.6) !important;
}

.dispatch-bg-signal13 {
    background-color: rgba(139, 0, 0, 0.8) !important;
}

.dispatch-bg-signal13-flash {
    position: relative !important;
}

.dispatch-bg-signal13-flash::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(180, 0, 0, 0.9);
    animation: signal13Flash 1.5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

.dispatch-bg-signal13-flash > * {
    position: relative;
    z-index: 2;
}

@keyframes signal13Flash {
    0% {
        background-color: rgba(180, 0, 0, 0.9);
    }
    50% {
        background-color: rgba(0, 0, 180, 0.9);
    }
    100% {
        background-color: rgba(180, 0, 0, 0.9);
    }
}

.dispatch-bg-tn {
    background-color: rgba(57, 0, 66, 0.46) !important;
}

.dispatch-bg-ranger-leo {
    background-color: #2F3E27B3 !important;
}

.dispatch-callsign {
    display: inline-block;
    min-width: 40px;
    text-transform: uppercase;
    margin-left: 5px;
    font-size: 11px;
    font-weight: bold;
    text-align: right;
}

.dispatch-name {
    text-transform: uppercase;
    font-size: 11px;
    font-weight: bold;
}

.dispatch-radio {
    text-transform: uppercase;
    font-size: 11px;
    font-weight: bold;
    color: #01b0f0;
}

.dispatch-btn-wrap {
  margin-right: 3px;
}

.dispatch-btn {
  all: unset;
  cursor: pointer;
  color: #01b0f0;
}

.police-notify {
    background-color: rgba(0, 0, 0, 0.7);
    color: whitesmoke !important;
}

.flash {
    width: 100%;
    animation: fading 4s infinite;
    -webkit-animation: fading 4s infinite;
    border-radius: 5px;
}

@keyframes fading {
    0%   { background: rgba(0, 0, 255, 0.69); }
    33%  { background: rgba(255, 0, 0, 0.69); }
    66%   { background: rgba(0, 0, 255, 0.69); }
    100%  { background: rgba(255, 0, 0, 0.69); }
}

@-webkit-keyframes fading {
    0%   { background: rgba(0, 0, 255, 0.69); }
    33%  { background: rgba(255, 0, 0, 0.69); }
    66%   { background: rgba(0, 0, 255, 0.69); }
    100%  { background: rgba(255, 0, 0, 0.69); }
}
@keyframes opacityfading {
    0%   { opacity: 0; }
    33%  { opacity: 1; }
    66%  { opacity: 0; }
    100% { opacity: 1; }
}

@-webkit-keyframes opacityfading {
    0%   { opacity: 0; }
    33%  { opacity: 1; }
    66%  { opacity: 0; }
    100% { opacity: 1; }
}

.close-info-key {
    position: absolute !important;
    right: 0 !important;
    color: white;
    background-color: rgba(0, 0, 0, 0.68);
    padding: 10px;
    margin: 20px;
    width: 300px
}

.blindfold-overlay {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  background: -moz-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 300px);
  background: -webkit-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 300px);
  background: -ms-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 300px);
  background: -o-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 300px);
  pointer-events: none;
}

.active-call {
    position: absolute;
    top: 20px;
    right: 20px;
}

.dot {
    height: 25px;
    width: 25px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
}

.detach-button {
    cursor: pointer;
    color: whitesmoke;
    background-color: #212121;
    margin: 15px;
    padding: 5px;
}

/* Modern Dispatch Navigation Bar */
.dispatch-navbar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    width: 700px;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    font-family: 'Poppins', sans-serif;
}

.dispatch-navbar-left {
    display: flex;
    align-items: center;
}

.dispatch-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

.dispatch-title i {
    color: #4a90e2;
    font-size: 16px;
}

.dispatch-navbar-center {
    display: flex;
    align-items: center;
    gap: 16px;
}

.dispatch-search-group {
    display: flex;
    align-items: center;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid #4a5568;
    border-radius: 6px;
    padding: 6px 32px 6px 32px;
    color: #ffffff;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;
    width: 180px;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.search-input::placeholder {
    color: #a0aec0;
    font-size: 11px;
}

.search-icon {
    position: absolute;
    left: 8px;
    color: #a0aec0;
    font-size: 11px;
    pointer-events: none;
}

.clear-search-btn {
    position: absolute;
    right: 6px;
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    font-size: 10px;
    transition: all 0.2s ease;
}

.clear-search-btn:hover {
    color: #e53e3e;
    background: rgba(229, 62, 62, 0.1);
}

.dispatch-sorting-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sorting-label {
    color: #cbd5e0;
    font-size: 12px;
    font-weight: 500;
    margin-right: 4px;
}

.sorting-buttons {
    display: flex;
    gap: 4px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 2px;
}

.sorting-btn {
    border: none;
    background: transparent;
    color: #a0aec0;
    font-size: 14px;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sorting-btn:hover {
    background: rgba(74, 144, 226, 0.2);
    color: #4a90e2;
}

.sorting-btn.active {
    background: #4a90e2;
    color: #ffffff;
    box-shadow: 0 1px 3px rgba(74, 144, 226, 0.5);
}

.dispatch-order-group {
    display: flex;
    align-items: center;
}

.order-btn {
    border: none;
    background: rgba(0, 0, 0, 0.2);
    color: #a0aec0;
    font-size: 14px;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.order-btn:hover {
    background: rgba(74, 144, 226, 0.2);
    color: #4a90e2;
}

.dispatch-navbar-right {
    display: flex;
    align-items: center;
}

.radio-info {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #cbd5e0;
    font-size: 11px;
    font-weight: 500;
    background: rgba(0, 0, 0, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
}

.radio-info i {
    color: #48bb78;
    font-size: 12px;
}

.dispatch-handle {
    background-color: #c80b0b;
    color: #fff;
    padding: 5px;
    cursor: move;
    text-align: center;
    user-select: none;
}

/* Department Indicator Line */
.department-indicator {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
}

.dispatch-profile {
    position: relative;
}

/* Status Dot Styles */
.dispatch-status-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
    vertical-align: middle;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

/* Pulsing animation for recently changed status */
.dispatch-pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulsate 1s ease-out infinite;
    opacity: 0.0;
    pointer-events: none;
}

@keyframes pulsate {
    0% {
        transform: translate(-50%, -50%) scale(0.1);
        opacity: 0.0;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0.0;
    }
}

/* Context Menu Styles */
.dispatch-context-menu {
    position: fixed;
    background: #2c2c2c;
    border: 1px solid #444;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 99999;
    min-width: 150px;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    pointer-events: auto;
}

.context-menu-item {
    padding: 8px 12px;
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.context-menu-item:hover {
    background-color: #4a90e2;
}

.context-menu-item:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.context-menu-item:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.context-menu-item i {
    width: 16px;
    text-align: center;
}

/* Context Menu Icon Colors */
.context-menu-item[data-action="callDuty"] i {
    color: #27ae60; /* Green for call */
}

.context-menu-item[data-action="routeGps"] i {
    color: #3498db; /* Blue for GPS */
}

.context-menu-item[data-action="setRadio"] i {
    color: #f39c12; /* Orange for radio */
}

.context-menu-item[data-action="setStatus"] i {
    color: #e74c3c; /* Red for status */
}

/* Input Dialog Styles */
.dispatch-input-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dispatch-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.dispatch-dialog-content {
    position: relative;
    background: #2c2c2c;
    border: 1px solid #444;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    min-width: 400px;
    max-width: 500px;
    font-family: 'Poppins', sans-serif;
}

.dispatch-dialog-header {
    padding: 15px 20px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dispatch-dialog-header h3 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

.dispatch-dialog-close {
    background: none;
    border: none;
    color: #ccc;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dispatch-dialog-close:hover {
    color: #fff;
}

.dispatch-dialog-body {
    padding: 20px;
}

.dispatch-dialog-body p {
    margin: 0 0 15px 0;
    color: #ccc;
    line-height: 1.4;
}

.dispatch-dialog-body input {
    width: 100%;
    padding: 10px;
    border: 1px solid #555;
    border-radius: 4px;
    background: #1a1a1a;
    color: #fff;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
}

.dispatch-dialog-body input:focus {
    outline: none;
    border-color: #4a90e2;
}

.dispatch-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #444;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dispatch-dialog-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
    transition: background-color 0.2s;
}

.dispatch-dialog-btn-cancel {
    background: #555;
    color: #fff;
}

.dispatch-dialog-btn-cancel:hover {
    background: #666;
}

.dispatch-dialog-btn-confirm {
    background: #4a90e2;
    color: #fff;
}

.dispatch-dialog-btn-confirm:hover {
    background: #357abd;
}



/* Status Selection Styles */
.dispatch-status-selection {
    margin-top: 10px;
}

.dispatch-status-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.dispatch-status-btn {
    padding: 10px 20px;
    border: 2px solid #4a90e2;
    border-radius: 4px;
    background: transparent;
    color: #4a90e2;
    cursor: pointer;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
    font-weight: bold;
    transition: all 0.2s;
    min-width: 60px;
}

.dispatch-status-btn:hover {
    background: #4a90e2;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.dispatch-status-btn:active {
    transform: translateY(0);
}

.dispatch-status-btn.selected {
    background: #4a90e2;
    color: #fff;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.5);
}

.dispatch-reason-section input {
    width: 100%;
    padding: 10px;
    border: 1px solid #555;
    border-radius: 4px;
    background: #1a1a1a;
    color: #fff;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
}

.dispatch-reason-section input:focus {
    outline: none;
    border-color: #4a90e2;
}

.dispatch-dialog-btn:disabled {
    background: #333;
    color: #666;
    cursor: not-allowed;
}

.dispatch-dialog-btn:disabled:hover {
    background: #333;
}

/* Clickable Radio Channels */
.dispatch-radio {
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 2px 4px;
}

/* Clickable Available Radio Channel */
.dispatch-available-radio {
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    text-decoration: underline;
    color: #4a90e2;
}

.dispatch-available-radio:hover {
    background-color: rgba(74, 144, 226, 0.2);
    color: #357abd;
}

.dispatch-radio:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

/* Radio Container */
.radio-container {
    display: inline-block;
    position: relative;
}

/* Enhanced Radio Channel Box */
.radio-channel-box {
    display: inline-block;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 11px;
    width: 48px;
    text-align: center;
    /*border: 1px solid rgba(255, 255, 255, 0.3);*/
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    box-sizing: border-box;
}

/* Radio Occupancy Indicator */
.radio-occupancy-indicator {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #191919;
    color: white;
    border-radius: 50%;
    width: 15px;
    height: 15px;
    font-size: 9px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #000;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

/* Proximity Group Visual Hierarchy */
.proximity-group-leader {
    position: relative;
    border-left: 3px solid #4a90e2 !important;
    margin-bottom: 2px;
}

.proximity-group-leader::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #4a90e2, rgba(74, 144, 226, 0.3));
    border-radius: 0 2px 2px 0;
}

.proximity-group-member {
    position: relative;
    margin-bottom: 1px;
}

/* Only indent the person section, keep radio/status columns aligned */
.proximity-group-member .dispatch-person {
    margin-left: 20px;
    position: relative;
    border-left: 2px solid rgba(74, 144, 226, 0.5) !important;
}

/* Connecting lines positioned relative to the person section */
.proximity-group-member::before {
    content: '';
    position: absolute;
    left: 18px;
    top: 50%;
    width: 18px;
    height: 2px;
    background: rgba(74, 144, 226, 0.5);
    transform: translateY(-50%);
}

.proximity-group-member::after {
    content: '';
    position: absolute;
    left: 18px;
    top: 0;
    width: 2px;
    height: 50%;
    background: rgba(74, 144, 226, 0.3);
}

/* Last member in group shouldn't have the vertical line extending down */
.proximity-group-member:last-of-type::after {
    display: none;
}

/* Subtle background tint for grouped members */
.proximity-group-leader,
.proximity-group-member {
    background-color: rgba(74, 144, 226, 0.05) !important;
}

/* Hover effects for proximity groups */
.proximity-group-leader:hover,
.proximity-group-member:hover {
    background-color: rgba(74, 144, 226, 0.15) !important;
    transform: translateX(2px);
    transition: all 0.2s ease;
}

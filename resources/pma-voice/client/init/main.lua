local mutedPlayers = {}

-- we can't use GetConvarInt because its not a integer, and theres no way to get a float... so use a hacky way it is!
local volumes = {
	-- people are setting this to 1 instead of 1.0 and expecting it to work.
	['radio'] = GetConvarInt('voice_defaultRadioVolume', 30) / 100,
	['phone'] = GetConvarInt('voice_defaultPhoneVolume', 60) / 100,
}

Citizen.CreateThread(function()
	Citizen.Wait(500)

	for _, volume_type in ipairs({
		'radio',
		'phone'
	}) do
		local volume_value = tonumber(tLocalStorage.get({ 'volume_' .. volume_type }) or '')

		if volume_value then
			volumes[volume_type] = math.floor(math.max(0, math.min(100, volume_value))) / 100
		end
	end
end)

radioEnabled, radioPressed, mode = true, false, GetConvarInt('voice_defaultVoiceMode', 2)
radioData = {}
callData = {}

exports('GetRadioData', function()
	return radioData
end)

--- function setVolume
--- Toggles the players volume
---@param volume number between 0 and 100
---@param volumeType string the volume type (currently radio & call) to set the volume of (opt)
function setVolume(volume, volumeType)
	type_check({volume, "number"})
	local volume = volume / 100

	if volumeType then
		local volumeTbl = volumes[volumeType]
		if volumeTbl then
			LocalPlayer.state:set(volumeType, volume, true)
			volumes[volumeType] = volume
		else
			error(('setVolume got a invalid volume type %s'):format(volumeType))
		end
	else
		-- _ is here to not mess with global 'type' function
		for _type, vol in pairs(volumes) do
			volumes[_type] = volume
			LocalPlayer.state:set(_type, volume, true)
		end
	end
end

TriggerEvent('chat:addSuggestionBLRP', '/volume', 'Display the current volume of the phone and radio')
TriggerEvent('chat:addSuggestionBLRP', '/volumeset', 'Set the volume of the phone and radio', {
	{ name = "type", help = "Volume type to set. \"phone\" or \"radio\""},
	{ name = "volume", help = "Desired volume level, 0-100 inclusive"}
})

exports('VolumeCommand', function()
	TriggerEvent('chat:addMessage', {
		template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
		args = { "Current phone volume: " .. math.floor(volumes['phone'] * 100) }
	})

	TriggerEvent('chat:addMessage', {
		template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
		args = { "Current radio volume: " .. math.floor(volumes['radio'] * 100) }
	})
end)

exports('VolumeSetCommand', function(args)
	if not args[2] or not args[3] or (args[2] ~= 'phone' and args[2] ~= 'radio') then
		TriggerEvent('chat:addMessage', {
			template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
			args = { "Invalid syntax" }
		})

		return
	end

	local volume = tonumber(args[3])

	if not volume or volume < 0 or volume > 100 then
		TriggerEvent('chat:addMessage', {
			template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
			args = { "Invalid syntax" }
		})

		return
	end

	volume = math.floor(volume)

	TriggerEvent('chat:addMessage', {
		template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
		args = { string.gsub(args[2], "^%l", string.upper) .. " volume set to " .. volume }
	})

	tLocalStorage.set({ 'volume_' .. args[2], volume })

	setVolume(volume, args[2])
end)

exports('setRadioVolume', function(vol)
	setVolume(vol, 'radio')
end)
exports('getRadioVolume', function()
	return volumes['radio']
end)
exports("setCallVolume", function(vol)
	setVolume(vol, 'phone')
end)
exports('getCallVolume', function()
	return volumes['phone']
end)

-- default submix incase people want to fiddle with it.
-- freq_low = 389.0
-- freq_hi = 3248.0
-- fudge = 0.0
-- rm_mod_freq = 0.0
-- rm_mix = 0.16
-- o_freq_lo = 348.0
-- 0_freq_hi = 4900.0

if gameVersion == 'fivem' then
	radioEffectId = CreateAudioSubmix('Radio')
	SetAudioSubmixEffectRadioFx(radioEffectId, 0)
	SetAudioSubmixEffectParamInt(radioEffectId, 0, `default`, 1)
	AddAudioSubmixOutput(radioEffectId, 0)

  radioEffectTooFar = CreateAudioSubmix('RadioU')
  SetAudioSubmixOutputVolumes(radioEffectTooFar, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)

  radioEffectFar = CreateAudioSubmix('RadioF')
  SetAudioSubmixEffectRadioFx(radioEffectFar, 1)
  SetAudioSubmixEffectParamInt(radioEffectFar, 1, GetHashKey('default'), 1)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('freq_low'), 100.0)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('freq_hi'), 5000.0)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('rm_mod_freq'), 300.0)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('rm_mix'), 0.8)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('fudge'), 16.0)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('o_freq_lo'), 300.0)
  SetAudioSubmixEffectParamFloat(radioEffectFar, 1, GetHashKey('o_freq_hi'), 5000.0)
  AddAudioSubmixOutput(radioEffectFar, 1)

  radioEffectMedium = CreateAudioSubmix('RadioM')
  SetAudioSubmixEffectRadioFx(radioEffectMedium, 1)
  SetAudioSubmixEffectParamInt(radioEffectMedium, 1, GetHashKey('default'), 1)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('freq_low'), 100.0)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('freq_hi'), 5000.0)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('rm_mod_freq'), 300.0)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('rm_mix'), 0.5)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('fudge'), 10.0)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('o_freq_lo'), 300.0)
  SetAudioSubmixEffectParamFloat(radioEffectMedium, 1, GetHashKey('o_freq_hi'), 5000.0)
  AddAudioSubmixOutput(radioEffectMedium, 1)

  radioEffectNear = CreateAudioSubmix('RadioN')
  SetAudioSubmixEffectRadioFx(radioEffectNear, 1)
  SetAudioSubmixEffectParamInt(radioEffectNear, 1, GetHashKey('default'), 1)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('freq_low'), 100.0)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('freq_hi'), 5000.0)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('rm_mod_freq'), 300.0)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('rm_mix'), 0.1)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('fudge'), 4.0)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('o_freq_lo'), 300.0)
  SetAudioSubmixEffectParamFloat(radioEffectNear, 1, GetHashKey('o_freq_hi'), 5000.0)
  AddAudioSubmixOutput(radioEffectNear, 1)

	phoneEffectId = CreateAudioSubmix('Phone')
	SetAudioSubmixEffectRadioFx(phoneEffectId, 1)
	SetAudioSubmixEffectParamInt(phoneEffectId, 1, `default`, 1)
	SetAudioSubmixEffectParamFloat(phoneEffectId, 1, `freq_low`, 300.0)
	SetAudioSubmixEffectParamFloat(phoneEffectId, 1, `freq_hi`, 6000.0)
	AddAudioSubmixOutput(phoneEffectId, 1)
end

local submixFunctions = {
	['radio'] = function(plySource, remote_coords)
    MumbleSetSubmixForServerId(plySource, radioEffectId)

    if
      remote_coords and
      GetResourceState('blrp_yankton') == 'started'
    then
      local local_inside = exports.blrp_zones:CoordsInYanktonParentZone(GetEntityCoords(PlayerPedId()))
      local remote_inside = exports.blrp_zones:CoordsInYanktonParentZone(remote_coords)

      if
        (local_inside and not remote_inside) or
        (remote_inside and not local_inside)
      then
        MumbleSetSubmixForServerId(plySource, radioEffectTooFar)
      end
    end
	end,
	['phone'] = function(plySource)
		MumbleSetSubmixForServerId(plySource, phoneEffectId)
	end
}

-- used to prevent a race condition if they talk again afterwards, which would lead to their voice going to default.
local disableSubmixReset = {}
--- function toggleVoice
--- Toggles the players voice
---@param plySource number the players server id to override the volume for
---@param enabled boolean if the players voice is getting activated or deactivated
---@param moduleType string the volume & submix to use for the voice.
function toggleVoice(plySource, enabled, moduleType, remote_coords)
	if mutedPlayers[plySource] then return end
	logger.verbose('[main] Updating %s to talking: %s with submix %s', plySource, enabled, moduleType)
	if enabled then
		MumbleSetVolumeOverrideByServerId(plySource, enabled and volumes[moduleType])
		if GetConvarInt('voice_enableSubmix', 1) == 1 and gameVersion == 'fivem' then
			if moduleType then
				disableSubmixReset[plySource] = true
				submixFunctions[moduleType](plySource, remote_coords)
			else
				MumbleSetSubmixForServerId(plySource, -1)
			end
		end
	else
		if GetConvarInt('voice_enableSubmix', 1) == 1 and gameVersion == 'fivem' then
			-- garbage collect it
			disableSubmixReset[plySource] = nil
			SetTimeout(250, function()
				if not disableSubmixReset[plySource] then
					MumbleSetSubmixForServerId(plySource, -1)
				end
			end)
		end
		MumbleSetVolumeOverrideByServerId(plySource, -1.0)
	end
end

--- function playerTargets
---Adds players voices to the local players listen channels allowing
---Them to communicate at long range, ignoring proximity range.
---@param targets table expects multiple tables to be sent over
function playerTargets(...)
	local targets = {...}
	local addedPlayers = {}

	local function processTarget(id)
		-- Don't add ourself
		if id == playerServerId then
			return
		end

		if addedPlayers[id] then
			logger.verbose('[main] %s is already target don\'t re-add', id)
			return
		end

		logger.verbose('[main] Adding %s as a voice target [1]', id)
		addedPlayers[id] = true
		MumbleAddVoiceTargetPlayerByServerId(voiceTarget, id)
	end

	for i = 1, #targets do
		for id, _ in pairs(targets[i]) do
			if type(_) == 'table' then
				-- Multi-radio compatibility
				for rid, _ in pairs(_) do
					processTarget(rid)
				end
			else
				processTarget(id)
			end
		end
	end
end

--- function playMicClicks
---plays the mic click if the player has them enabled.
---@param clickType boolean whether to play the 'on' or 'off' click.
function playMicClicks(clickType)
  local radio_sound = exports.blrp_core:LocalStorageGet('radio_sounds')
  local volume_setting = exports.blrp_core:LocalStorageGet('mic_click_volume') or '100%'

  -- Convert percentage string to decimal multiplier (e.g., "75%" -> 0.75)
  local volume_number_str = volume_setting:gsub('%%', '')
  local volume_multiplier = tonumber(volume_number_str) / 100

  if not radio_sound or radio_sound == 'Old' then
    sendUIMessage({
      sound = (clickType and 'audio_on' or 'audio_off'),
      volume = 0.03 * volume_multiplier
    })
  else
    sendUIMessage({
      sound = (clickType and 'mic_click_on' or 'mic_click_off'),
      volume = (clickType and 0.30 or 0.06) * volume_multiplier
    })
  end
end

function playBlockSound()
	sendUIMessage({
		sound = 'audio_blocked',
		volume = 0.3
	})
end

RegisterNetEvent('pma-voice:client:playBlockSound', playBlockSound)

--- toggles the targeted player muted
---@param source number the player to mute
function toggleMutePlayer(source)
	if mutedPlayers[source] then
		mutedPlayers[source] = nil
		MumbleSetVolumeOverrideByServerId(source, -1.0)
	else
		mutedPlayers[source] = true
		MumbleSetVolumeOverrideByServerId(source, 0.0)
	end
end
exports('toggleMutePlayer', toggleMutePlayer)

-- cache their external servers so if it changes in runtime we can reconnect the client.
local externalAddress = ''
local externalPort = 0
CreateThread(function()
	while true do
		Wait(500)
		-- only change if what we have doesn't match the cache
		if GetConvar('voice_externalAddress', '') ~= externalAddress or GetConvarInt('voice_externalPort', 0) ~= externalPort then
			externalAddress = GetConvar('voice_externalAddress', '')
			externalPort = GetConvarInt('voice_externalPort', 0)
			-- MumbleSetServerAddress(GetConvar('voice_externalAddress', ''), GetConvarInt('voice_externalPort', 0))
		end
	end
end)

RegisterCommand("fixVoice", function(source, args, rawCommand)
	-- MumbleSetServerAddress('0.0.0.0', 30130)
	print(MumbleIsConnected())
	Citizen.Wait(1000)
	-- MumbleSetServerAddress(GetConvar('voice_externalAddress', ''), GetConvarInt('voice_externalPort', 0))
	print(MumbleIsConnected())
end, false)

local routing_bucket_initialized = false

RegisterNetEvent('core:client:instancing:routingBucketChanged', function(routing_bucket)
	if routing_bucket_initialized then
		return
	end

	if routing_bucket ~= 0 then
		return
	end

	-- MumbleSetServerAddress('0.0.0.0', 30130)
	Citizen.Wait(1000)
	-- MumbleSetServerAddress(GetConvar('voice_externalAddress', ''), GetConvarInt('voice_externalPort', 0))

	routing_bucket_initialized = true
end)

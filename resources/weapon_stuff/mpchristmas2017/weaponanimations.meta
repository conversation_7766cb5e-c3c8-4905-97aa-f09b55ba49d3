<CWeaponAnimationsSets>
  <WeaponAnimationsSets>
    <Item key="Ballistic">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <MotionClipSetHash>move_ballistic_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MotionClipSetHash>move_ballistic_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <MotionClipSetHash>move_ballistic_2h</MotionClipSetHash>
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <MotionClipSetHash>move_ballistic_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="Default">
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <CoverMovementClipSetHash>cover@move@ai@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_Wpn_RifleBullpup</CoverWeaponClipSetHash>
          <MotionClipSetHash>anim@weapons@submg@bullpup_rifle_mk2@grip_fallback</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@submg@bullpup_rifle_mk2@grip_fallback</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@submg@bullpup_rifle_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@submg@bullpup_rifle@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
        </Item>
        <Item key="WEAPON_DOUBLEACTION">
          <CoverMovementClipSetHash>cover@move@ai@base@1h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@doubleaction</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@pistol@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@pistol@doubleaction</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@pistol@doubleaction_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@pistol@doubleaction@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <WeaponSwapClipSetHash>anim@weapons@pistol@doubleaction_holster</WeaponSwapClipSetHash>
		  <AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <CoverMovementClipSetHash>cover@move@ai@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_Wpn_RifleHi</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@heavy@rpg</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@rifle@hi@assault_rifle</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@rifle@hi@assault_rifle_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@rifle@hi@assault_rifle@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <CoverMovementClipSetHash>cover@move@ai@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@rifle@pump_shotgun_mk2</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@heavy@rpg</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@rifle@lo@pump</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@rifle@lo@pump_mk2_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@rifle@lo@pump@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
        </Item>
        <Item key="WEAPON_REVOLVER_MK2">
          <CoverMovementClipSetHash>cover@move@ai@base@1h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@revolver_mk2</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@pistol@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@pistol@revolver_mk2</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@pistol@revolver_mk2_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@pistol@revolver_mk2@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
        </Item>
        <Item key="WEAPON_SNSPISTOL_MK2">
          <CoverMovementClipSetHash>cover@move@ai@base@1h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_Wpn_Pistol</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@pistol@pistol</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@pistol@pistol</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@pistol@pistol_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>weapons@pistol@pistol@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />	
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <CoverMovementClipSetHash>cover@move@ai@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_Wpn_RifleSpCarbine</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@heavy@rpg</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@rifle@lo@spcarbine</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@rifle@lo@spcarbine_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@rifle@lo@spcarbine@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="Gang">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
	    <Item key="WEAPON_DOUBLEACTION">
          <FiringVariationsStandingClipSetHash>combat_fire_variations_gang_pistol</FiringVariationsStandingClipSetHash>
        </Item>
	    <Item key="WEAPON_REVOLVER_MK2">
          <FiringVariationsStandingClipSetHash>combat_fire_variations_gang_pistol</FiringVariationsStandingClipSetHash>
        </Item>
	    <Item key="WEAPON_SNSPISTOL_MK2">
          <FiringVariationsStandingClipSetHash>combat_fire_variations_gang_pistol</FiringVariationsStandingClipSetHash>
        </Item>
	  </WeaponAnimations>
	</Item>	
    <Item key="MP_F_Freemode">
      <Fallback>Gang</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <MotionClipSetHash>weapons@submg@bullpup_rifle@f</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MotionClipSetHash>weapons@rifle@hi@assault_rifle@f</MotionClipSetHash>
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <MotionClipSetHash>weapons@rifle@lo@pump@f</MotionClipSetHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <MotionClipSetHash>weapons@rifle@lo@spcarbine@f</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="FirstPerson">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleBullpup</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@advanced_rifle@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@assault_rifle@bullpup_rifle@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@bullpup_rifle@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@assault_rifle@bullpup_rifle@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
		  <FPSFidgetClipsetHashes>
			<Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@fidgets@a</Item>
			<Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@fidgets@b</Item>
			<Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@fidgets@c</Item>
		  </FPSFidgetClipsetHashes>
 		</Item>
        <Item key="WEAPON_DOUBLEACTION">
		  <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@doubleaction@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_idle@generic@pistol@doubleaction@w_idle</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@doubleaction@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_stealth@generic@pistol@doubleaction@w_idle</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash></FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
		  <FPSFidgetClipsetHashes>
			<Item>anim@weapons@first_person@aim_idle@p_m_zero@pistol@doubleaction@fidgets@a</Item>
          </FPSFidgetClipsetHashes>
		  <WeaponClipSetHashForClone>anim@weapons@first_person@aim_idle@remote_clone@pistol@doubleaction@w_idle</WeaponClipSetHashForClone>
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleHi</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@sniper_rifle@marksman_rifle@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@assault_rifle_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@assault_rifle@shared@core</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@sniper_rifle@marksman_rifle@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
          <WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@assault_rifle@shared@core</WeaponClipSetHashForClone>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_idle@p_m_zero@sniper_rifle@marksman_rifle@fidgets@a</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@sniper_rifle@marksman_rifle@fidgets@b</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@sniper_rifle@marksman_rifle@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
		  <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@first_person@weapon@reloads@rifle@pump_shotgun_mk2</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_idle@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@assault_rifle@shared@core</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@shotgun@pump_shotgun_mk2_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@shotgun@shared@core</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash> 
	        <WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@assault_rifle@shared@core</WeaponClipSetHashForClone>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@fidgets@a</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@fidgets@b</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_REVOLVER_MK2">
		  <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@revolver_mk2@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_idle@generic@pistol@revolver_mk2@w_idle</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@revolver_mk2@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_stealth@generic@pistol@revolver_mk2@w_idle</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash></FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
		  <FPSFidgetClipsetHashes>
			<Item>anim@weapons@first_person@aim_idle@p_m_zero@pistol@revolver_mk2@fidgets@a</Item>
          </FPSFidgetClipsetHashes>
		  <WeaponClipSetHashForClone>anim@weapons@first_person@aim_idle@remote_clone@pistol@revolver_mk2@w_idle</WeaponClipSetHashForClone>
        </Item>
        <Item key="WEAPON_SNSPISTOL_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_idle@generic@pistol@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@pistol@shared@core</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@pistol@shared@core</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash></FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_idle@p_m_zero@pistol@shared@fidgets@a</Item>
			<Item>weapons@first_person@aim_idle@p_m_zero@pistol@shared@fidgets@b</Item>
			<Item>weapons@first_person@aim_idle@p_m_zero@pistol@shared@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleSpCarbine</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_idle@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@assault_rifle@special_carbine@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@special_carbine@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@assault_rifle@special_carbine@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@special_carbine@fidgets@a</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@special_carbine@fidgets@b</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@assault_rifle@special_carbine@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="FirstPersonAiming">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleBullpup</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@advanced_rifle@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_lt@generic@assault_rifle@bullpup_rifle@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@bullpup_rifle@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_lt@generic@assault_rifle@bullpup_rifle@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
		  <FPSFidgetClipsetHashes>
			<Item>weapons@first_person@aim_lt@p_m_zero@assault_rifle@bullpup_rifle@fidgets@a</Item>
			<Item>weapons@first_person@aim_lt@p_m_zero@assault_rifle@bullpup_rifle@fidgets@b</Item>
			<Item>weapons@first_person@aim_lt@p_m_zero@assault_rifle@bullpup_rifle@fidgets@c</Item>
		  </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_DOUBLEACTION">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@doubleaction@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_lt@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_lt@generic@pistol@doubleaction@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@doubleaction@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_lt@generic@pistol@doubleaction@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
		  <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
          <FPSFidgetClipsetHashes>
            <Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@doubleaction@fidgets@a</Item>
			<Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@doubleaction@fidgets@b</Item>
			<Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@doubleaction@fidgets@c</Item>
			<Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@doubleaction@fidgets@d</Item>
          </FPSFidgetClipsetHashes>			        
		</Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleHi</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@sniper_rifle@marksman_rifle@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@assault_rifle_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@generic@sniper_rifle@marksman_rifle@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@first_person@weapon@reloads@rifle@pump_shotgun_mk2</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_lt@generic@shotgun@shared@core@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@shotgun@pump_shotgun_mk2_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_lt@generic@shotgun@shared@core@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
	        <FPSFidgetClipsetHashes>
             <Item>weapons@first_person@aim_lt@p_m_zero@shotgun@shared@fidgets@a</Item>
		     <Item>weapons@first_person@aim_lt@p_m_zero@shotgun@shared@fidgets@b</Item>
		     <Item>weapons@first_person@aim_lt@p_m_zero@shotgun@shared@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
	    </Item>
        <Item key="WEAPON_REVOLVER_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@revolver_mk2@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_lt@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_lt@generic@pistol@revolver_mk2@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@revolver_mk2@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_lt@generic@pistol@revolver_mk2@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
		  <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
          <FPSFidgetClipsetHashes>
            <Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@revolver_mk2@fidgets@a</Item>
			<Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@revolver_mk2@fidgets@b</Item>
			<Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@revolver_mk2@fidgets@c</Item>
			<Item>anim@weapons@first_person@aim_lt@p_m_zero@pistol@revolver_mk2@fidgets@d</Item>
          </FPSFidgetClipsetHashes>
		</Item>
        <Item key="WEAPON_SNSPISTOL_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_lt@generic@pistol@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>weapons@first_person@aim_lt@generic@pistol@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@a</Item>
			<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@b</Item>
			<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@c</Item>
			<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@d</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleSpCarbine</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_lt@generic@assault_rifle@special_carbine@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@special_carbine@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_lt@generic@assault_rifle@special_carbine@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
		  <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_lt@p_m_zero@assault_rifle@special_carbine@fidgets@a</Item>
	        <Item>weapons@first_person@aim_lt@p_m_zero@assault_rifle@special_carbine@fidgets@b</Item>
	        <Item>weapons@first_person@aim_lt@p_m_zero@assault_rifle@special_carbine@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="FirstPersonRNG">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleBullpup</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@advanced_rifle@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@bullpup_rifle@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@bullpup_rifle@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@generic@assault_rifle@bullpup_rifle@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
		  <FPSFidgetClipsetHashes>
			<Item>weapons@first_person@aim_rng@p_m_zero@assault_rifle@bullpup_rifle@fidgets@a</Item>
			<Item>weapons@first_person@aim_rng@p_m_zero@assault_rifle@bullpup_rifle@fidgets@b</Item>
			<Item>weapons@first_person@aim_rng@p_m_zero@assault_rifle@bullpup_rifle@fidgets@c</Item>
		  </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_DOUBLEACTION">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@doubleaction@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_rng@generic@pistol@doubleaction@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@doubleaction@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_rng@generic@pistol@doubleaction@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_rng</FPSTransitionFromLTHash>	
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
		  <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>	
		  <FPSFidgetClipsetHashes>
            <Item>anim@weapons@first_person@aim_rng@p_m_zero@pistol@doubleaction@fidgets@a</Item>
			<Item>anim@weapons@first_person@aim_rng@p_m_zero@pistol@doubleaction@fidgets@b</Item>
          </FPSFidgetClipsetHashes>      
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleHi</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@sniper_rifle@marksman_rifle@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@assault_rifle_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@generic@sniper_rifle@marksman_rifle@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@sniper_rifle@marksman_rifle@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_rng@p_m_zero@sniper_rifle@marksman_rifle@fidgets@a</Item>
            <Item>weapons@first_person@aim_rng@p_m_zero@sniper_rifle@marksman_rifle@fidgets@b</Item>
            <Item>weapons@first_person@aim_rng@p_m_zero@sniper_rifle@marksman_rifle@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@first_person@weapon@reloads@rifle@pump_shotgun_mk2</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@shotgun@shared@core@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@shotgun@pump_shotgun_mk2_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@generic@shotgun@shared@core@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
	  	    <FPSFidgetClipsetHashes>
             <Item>weapons@first_person@aim_rng@p_m_zero@shotgun@shared@fidgets@a</Item>
	         <Item>weapons@first_person@aim_rng@p_m_zero@shotgun@shared@fidgets@b</Item>
	         <Item>weapons@first_person@aim_rng@p_m_zero@shotgun@shared@fidgets@c</Item>
          </FPSFidgetClipsetHashes>        
        </Item>
        <Item key="WEAPON_REVOLVER_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@revolver_mk2@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_rng@generic@pistol@revolver_mk2@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@revolver_mk2@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_rng@generic@pistol@revolver_mk2@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_rng</FPSTransitionFromLTHash>	
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
		  <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>	
		  <FPSFidgetClipsetHashes>
            <Item>anim@weapons@first_person@aim_rng@p_m_zero@pistol@revolver_mk2@fidgets@a</Item>
			<Item>anim@weapons@first_person@aim_rng@p_m_zero@pistol@revolver_mk2@fidgets@b</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_SNSPISTOL_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@pistol@pistol</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@pistol@pistol</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_rng@p_m_zero@pistol@shared@fidgets@a</Item>
			<Item>weapons@first_person@aim_rng@p_m_zero@pistol@shared@fidgets@b</Item>
			<Item>weapons@first_person@aim_rng@p_m_zero@pistol@shared@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleSpCarbine</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@special_carbine@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@special_carbine@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@generic@assault_rifle@special_carbine@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
		  <FPSFidgetClipsetHashes>
			<Item>weapons@first_person@aim_rng@p_m_zero@assault_rifle@special_carbine@fidgets@a</Item>
			<Item>weapons@first_person@aim_rng@p_m_zero@assault_rifle@special_carbine@fidgets@b</Item>
			<Item>weapons@first_person@aim_rng@p_m_zero@assault_rifle@special_carbine@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="FirstPersonScope">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleBullpup</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@advanced_rifle@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_scope@generic@assault_rifle@bullpup_rifle@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@bullpup_rifle@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@assault_rifle@bullpup_rifle@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_DOUBLEACTION">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@doubleaction@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_scope@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_scope@generic@pistol@doubleaction@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@doubleaction@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_scope@generic@pistol@doubleaction@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_scope</FPSTransitionFromLTHash>	
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
		  <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleHi</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_scope@generic@sniper_rifle@marksman_rifle_mk2@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@assault_rifle_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@sniper_rifle@marksman_rifle_mk2@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@sniper_rifle@marksman_rifle@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@sniper_rifle@marksman_rifle@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@sniper_rifle@marksman_rifle@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@first_person@weapon@reloads@rifle@pump_shotgun_mk2</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_scope@generic@shotgun@shared@core@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@shotgun@pump_shotgun_mk2_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@shotgun@shared@core@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_REVOLVER_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>anim@cover@weapon@reloads@pistol@revolver_mk2@first_person</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_scope@generic@pistol@pistol_50@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>anim@weapons@first_person@aim_scope@generic@pistol@revolver_mk2@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>anim@weapons@first_person@aim_rng@general@pistol@revolver_mk2@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>anim@weapons@first_person@aim_scope@generic@pistol@revolver_mk2@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
		  <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@pistol_50@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@pistol_50@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@pistol_50@aim_trans@lt_to_scope</FPSTransitionFromLTHash>	
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@pistol_50@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
		  <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@pistol@pistol_50@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_SNSPISTOL_MK2">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_scope@generic@pistol@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
          <WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@pistol@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="3.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RifleSpCarbine</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_scope@generic@assault_rifle@special_carbine@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@assault_rifle@special_carbine@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@assault_rifle@special_carbine@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash>combat_fire_variations_rifle</FiringVariationsStandingClipSetHash>
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash>combat_aim_turns_rifle</AimTurnStandingClipSetHash>
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</AimGrenadeThrowNormalClipsetHash>
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="Fat">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <MotionClipSetHash>move_fat_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MotionClipSetHash>move_fat_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <MotionClipSetHash>move_fat_2h</MotionClipSetHash>
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <MotionClipSetHash>move_fat_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="SuperFat">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <MotionClipSetHash>move_superfat_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
		  <WeaponSwapClipSetHash>weapons@holster_superfat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MotionClipSetHash>move_superfat_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
		  <WeaponSwapClipSetHash>weapons@holster_superfat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <MotionClipSetHash>move_superfat_2h</MotionClipSetHash>
		  <WeaponSwapClipSetHash>weapons@holster_superfat_2h</WeaponSwapClipSetHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <MotionClipSetHash>move_superfat_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
		  <WeaponSwapClipSetHash>weapons@holster_superfat_2h</WeaponSwapClipSetHash>
        </Item>
	  </WeaponAnimations>
    </Item>
    <Item key="Female">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <MotionClipSetHash>move_female_2h_carbine_rifle</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
        </Item>
	  </WeaponAnimations>
	</Item>	  
	<Item key="GangFemale">
      <Fallback>Gang</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_BULLPUPRIFLE_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
        </Item>
        <Item key="WEAPON_MARKSMANRIFLE_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.500000" />
          <AnimBlindFireRateModifier value="0.500000" />
        </Item>
        <Item key="WEAPON_PUMPSHOTGUN_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
        </Item>
        <Item key="WEAPON_SPECIALCARBINE_MK2">
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
          <AnimFireRateModifier value="0.800000" />
          <AnimBlindFireRateModifier value="0.800000" />
        </Item>
	  </WeaponAnimations>
	</Item>
  </WeaponAnimationsSets>
</CWeaponAnimationsSets>
var $l=Object.defineProperty;var Al=(e,t,n)=>t in e?$l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var H=(e,t,n)=>(Al(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&i(r)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();function ht(){}const ta=e=>e;function N(e,t){for(const n in t)e[n]=t[n];return e}function ea(e){return e()}function rs(){return Object.create(null)}function te(e){e.forEach(ea)}function le(e){return typeof e=="function"}function Y(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Pl(e){return Object.keys(e).length===0}function Ds(e,...t){if(e==null)return ht;const n=e.subscribe(...t);return n.unsubscribe?()=>n.unsubscribe():n}function Fl(e){let t;return Ds(e,n=>t=n)(),t}function gt(e,t,n){e.$$.on_destroy.push(Ds(t,n))}function At(e,t,n,i){if(e){const s=na(e,t,n,i);return e[0](s)}}function na(e,t,n,i){return e[1]&&i?N(n.ctx.slice(),e[1](i(t))):n.ctx}function Pt(e,t,n,i){if(e[2]&&i){const s=e[2](i(n));if(t.dirty===void 0)return s;if(typeof s=="object"){const o=[],r=Math.max(t.dirty.length,s.length);for(let a=0;a<r;a+=1)o[a]=t.dirty[a]|s[a];return o}return t.dirty|s}return t.dirty}function Ft(e,t,n,i,s,o){if(s){const r=na(t,n,i,o);e.p(r,s)}}function Dt(e){if(e.ctx.length>32){const t=[],n=e.ctx.length/32;for(let i=0;i<n;i++)t[i]=-1;return t}return-1}function nt(e){const t={};for(const n in e)n[0]!=="$"&&(t[n]=e[n]);return t}function io(e,t){const n={};t=new Set(t);for(const i in e)!t.has(i)&&i[0]!=="$"&&(n[i]=e[i]);return n}function bt(e,t,n){return e.set(n),t}function Os(e){return e&&le(e.destroy)?e.destroy:ht}const ia=typeof window<"u";let Dl=ia?()=>window.performance.now():()=>Date.now(),Es=ia?e=>requestAnimationFrame(e):ht;const Ge=new Set;function sa(e){Ge.forEach(t=>{t.c(e)||(Ge.delete(t),t.f())}),Ge.size!==0&&Es(sa)}function Ol(e){let t;return Ge.size===0&&Es(sa),{promise:new Promise(n=>{Ge.add(t={c:e,f:n})}),abort(){Ge.delete(t)}}}let $i=!1;function El(){$i=!0}function Ll(){$i=!1}function Tl(e,t,n,i){for(;e<t;){const s=e+(t-e>>1);n(s)<=i?e=s+1:t=s}return e}function Il(e){if(e.hydrate_init)return;e.hydrate_init=!0;let t=e.childNodes;if(e.nodeName==="HEAD"){const l=[];for(let c=0;c<t.length;c++){const u=t[c];u.claim_order!==void 0&&l.push(u)}t=l}const n=new Int32Array(t.length+1),i=new Int32Array(t.length);n[0]=-1;let s=0;for(let l=0;l<t.length;l++){const c=t[l].claim_order,u=(s>0&&t[n[s]].claim_order<=c?s+1:Tl(1,s,d=>t[n[d]].claim_order,c))-1;i[l]=n[u]+1;const f=u+1;n[f]=l,s=Math.max(f,s)}const o=[],r=[];let a=t.length-1;for(let l=n[s]+1;l!=0;l=i[l-1]){for(o.push(t[l-1]);a>=l;a--)r.push(t[a]);a--}for(;a>=0;a--)r.push(t[a]);o.reverse(),r.sort((l,c)=>l.claim_order-c.claim_order);for(let l=0,c=0;l<r.length;l++){for(;c<o.length&&r[l].claim_order>=o[c].claim_order;)c++;const u=c<o.length?o[c]:null;e.insertBefore(r[l],u)}}function w(e,t){e.appendChild(t)}function oa(e){if(!e)return document;const t=e.getRootNode?e.getRootNode():e.ownerDocument;return t&&t.host?t:e.ownerDocument}function Vl(e){const t=A("style");return zl(oa(e),t),t.sheet}function zl(e,t){return w(e.head||e,t),t.sheet}function Hl(e,t){if($i){for(Il(e),(e.actual_end_child===void 0||e.actual_end_child!==null&&e.actual_end_child.parentNode!==e)&&(e.actual_end_child=e.firstChild);e.actual_end_child!==null&&e.actual_end_child.claim_order===void 0;)e.actual_end_child=e.actual_end_child.nextSibling;t!==e.actual_end_child?(t.claim_order!==void 0||t.parentNode!==e)&&e.insertBefore(t,e.actual_end_child):e.actual_end_child=t.nextSibling}else(t.parentNode!==e||t.nextSibling!==null)&&e.appendChild(t)}function it(e,t,n){e.insertBefore(t,n||null)}function Nl(e,t,n){$i&&!n?Hl(e,t):(t.parentNode!==e||t.nextSibling!=n)&&e.insertBefore(t,n||null)}function U(e){e.parentNode&&e.parentNode.removeChild(e)}function Xe(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function A(e){return document.createElement(e)}function ra(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function J(e){return document.createTextNode(e)}function q(){return J(" ")}function kn(){return J("")}function It(e,t,n,i){return e.addEventListener(t,n,i),()=>e.removeEventListener(t,n,i)}function D(e,t,n){n==null?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}function Bl(e,t){const n=Object.getOwnPropertyDescriptors(e.__proto__);for(const i in t)t[i]==null?e.removeAttribute(i):i==="style"?e.style.cssText=t[i]:i==="__value"?e.value=e[i]=t[i]:n[i]&&n[i].set?e[i]=t[i]:D(e,i,t[i])}function ii(e,t){for(const n in t)D(e,n,t[n])}function jl(e){return Array.from(e.childNodes)}function Wl(e){e.claim_info===void 0&&(e.claim_info={last_index:0,total_claimed:0})}function Gl(e,t,n,i,s=!1){Wl(e);const o=(()=>{for(let r=e.claim_info.last_index;r<e.length;r++){const a=e[r];if(t(a)){const l=n(a);return l===void 0?e.splice(r,1):e[r]=l,s||(e.claim_info.last_index=r),a}}for(let r=e.claim_info.last_index-1;r>=0;r--){const a=e[r];if(t(a)){const l=n(a);return l===void 0?e.splice(r,1):e[r]=l,s?l===void 0&&e.claim_info.last_index--:e.claim_info.last_index=r,a}}return i()})();return o.claim_order=e.claim_info.total_claimed,e.claim_info.total_claimed+=1,o}function ql(e,t){return Gl(e,n=>n.nodeType===3,n=>{const i=""+t;if(n.data.startsWith(i)){if(n.data.length!==i.length)return n.splitText(i.length)}else n.data=i},()=>J(t),!0)}function Wt(e,t){t=""+t,e.wholeText!==t&&(e.data=t)}function si(e,t){e.value=t??""}function Fn(e,t,n){e.classList[n?"add":"remove"](t)}function Yl(e,t,{bubbles:n=!1,cancelable:i=!1}={}){const s=document.createEvent("CustomEvent");return s.initCustomEvent(e,n,i,t),s}function ye(e,t){return new e(t)}const oi=new Map;let ri=0;function Ul(e){let t=5381,n=e.length;for(;n--;)t=(t<<5)-t^e.charCodeAt(n);return t>>>0}function Xl(e,t){const n={stylesheet:Vl(t),rules:{}};return oi.set(e,n),n}function so(e,t,n,i,s,o,r,a=0){const l=16.666/i;let c=`{
`;for(let m=0;m<=1;m+=l){const _=t+(n-t)*o(m);c+=m*100+`%{${r(_,1-_)}}
`}const u=c+`100% {${r(n,1-n)}}
}`,f=`__svelte_${Ul(u)}_${a}`,d=oa(e),{stylesheet:h,rules:g}=oi.get(d)||Xl(d,e);g[f]||(g[f]=!0,h.insertRule(`@keyframes ${f} ${u}`,h.cssRules.length));const p=e.style.animation||"";return e.style.animation=`${p?`${p}, `:""}${f} ${i}ms linear ${s}ms 1 both`,ri+=1,f}function Kl(e,t){const n=(e.style.animation||"").split(", "),i=n.filter(t?o=>o.indexOf(t)<0:o=>o.indexOf("__svelte")===-1),s=n.length-i.length;s&&(e.style.animation=i.join(", "),ri-=s,ri||Ql())}function Ql(){Es(()=>{ri||(oi.forEach(e=>{const{ownerNode:t}=e.stylesheet;t&&U(t)}),oi.clear())})}let Oe;function Pe(e){Oe=e}function tn(){if(!Oe)throw new Error("Function called outside component initialization");return Oe}function Ai(e){tn().$$.on_mount.push(e)}function Zl(e){tn().$$.after_update.push(e)}function en(e){tn().$$.on_destroy.push(e)}function Jl(e,t){return tn().$$.context.set(e,t),t}function Ls(e){return tn().$$.context.get(e)}function tc(e){return tn().$$.context.has(e)}function ec(e,t){const n=e.$$.callbacks[t.type];n&&n.slice().forEach(i=>i.call(this,t))}const je=[],Ke=[];let qe=[];const as=[],aa=Promise.resolve();let ls=!1;function la(){ls||(ls=!0,aa.then(ua))}function Ts(){return la(),aa}function Qe(e){qe.push(e)}function ca(e){as.push(e)}const Vi=new Set;let ze=0;function ua(){if(ze!==0)return;const e=Oe;do{try{for(;ze<je.length;){const t=je[ze];ze++,Pe(t),nc(t.$$)}}catch(t){throw je.length=0,ze=0,t}for(Pe(null),je.length=0,ze=0;Ke.length;)Ke.pop()();for(let t=0;t<qe.length;t+=1){const n=qe[t];Vi.has(n)||(Vi.add(n),n())}qe.length=0}while(je.length);for(;as.length;)as.pop()();ls=!1,Vi.clear(),Pe(e)}function nc(e){if(e.fragment!==null){e.update(),te(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(Qe)}}function ic(e){const t=[],n=[];qe.forEach(i=>e.indexOf(i)===-1?t.push(i):n.push(i)),n.forEach(i=>i()),qe=t}let nn;function sc(){return nn||(nn=Promise.resolve(),nn.then(()=>{nn=null})),nn}function zi(e,t,n){e.dispatchEvent(Yl(`${t?"intro":"outro"}${n}`))}const Kn=new Set;let ae;function kt(){ae={r:0,c:[],p:ae}}function Rt(){ae.r||te(ae.c),ae=ae.p}function S(e,t){e&&e.i&&(Kn.delete(e),e.i(t))}function C(e,t,n,i){if(e&&e.o){if(Kn.has(e))return;Kn.add(e),ae.c.push(()=>{Kn.delete(e),i&&(n&&e.d(1),i())}),e.o(t)}else i&&i()}const oc={duration:0};function ai(e,t,n,i){const s={direction:"both"};let o=t(e,n,s),r=i?0:1,a=null,l=null,c=null;function u(){c&&Kl(e,c)}function f(h,g){const p=h.b-r;return g*=Math.abs(p),{a:r,b:h.b,d:p,duration:g,start:h.start,end:h.start+g,group:h.group}}function d(h){const{delay:g=0,duration:p=300,easing:m=ta,tick:_=ht,css:b}=o||oc,y={start:Dl()+g,b:h};h||(y.group=ae,ae.r+=1),a||l?l=y:(b&&(u(),c=so(e,r,h,p,g,m,b)),h&&_(0,1),a=f(y,p),Qe(()=>zi(e,h,"start")),Ol(x=>{if(l&&x>l.start&&(a=f(l,p),l=null,zi(e,a.b,"start"),b&&(u(),c=so(e,r,a.b,a.duration,0,m,o.css))),a){if(x>=a.end)_(r=a.b,1-r),zi(e,a.b,"end"),l||(a.b?u():--a.group.r||te(a.group.c)),a=null;else if(x>=a.start){const v=x-a.start;r=a.a+a.d*m(v/a.duration),_(r,1-r)}}return!!(a||l)}))}return{run(h){le(o)?sc().then(()=>{o=o(s),d(h)}):d(h)},end(){u(),a=l=null}}}function zt(e,t){const n={},i={},s={$$scope:1};let o=e.length;for(;o--;){const r=e[o],a=t[o];if(a){for(const l in r)l in a||(i[l]=1);for(const l in a)s[l]||(n[l]=a[l],s[l]=1);e[o]=a}else for(const l in r)s[l]=1}for(const r in i)r in n||(n[r]=void 0);return n}function ee(e){return typeof e=="object"&&e!==null?e:{}}const rc=/[&"]/g,ac=/[&<]/g;function lc(e,t=!1){const n=String(e),i=t?rc:ac;i.lastIndex=0;let s="",o=0;for(;i.test(n);){const r=i.lastIndex-1,a=n[r];s+=n.substring(o,r)+(a==="&"?"&amp;":a==='"'?"&quot;":"&lt;"),o=r+1}return s+n.substring(o)}function cc(e,t){if(!e||!e.$$render)throw t==="svelte:component"&&(t+=" this={...}"),new Error(`<${t}> is not a valid SSR component. You may need to review your build config to ensure that dependencies are compiled, rather than imported as pre-compiled modules. Otherwise you may need to fix a <${t}>.`);return e}let Hi;function fa(e){function t(n,i,s,o,r){const a=Oe,l={on_destroy:Hi,context:new Map(r||(a?a.$$.context:[])),on_mount:[],before_update:[],after_update:[],callbacks:rs()};Pe({$$:l});const c=e(n,i,s,o);return Pe(a),c}return{render:(n={},{$$slots:i={},context:s=new Map}={})=>{Hi=[];const o={title:"",head:"",css:new Set},r=t(o,n,{},i,s);return te(Hi),{html:r,css:{code:Array.from(o.css).map(a=>a.code).join(`
`),map:null},head:o.title+o.head}},$$render:t}}function da(e,t,n){const i=e.$$.props[t];i!==void 0&&(e.$$.bound[i]=n,n(e.$$.ctx[i]))}function V(e){e&&e.c()}function uc(e,t){e&&e.l(t)}function E(e,t,n,i){const{fragment:s,after_update:o}=e.$$;s&&s.m(t,n),i||Qe(()=>{const r=e.$$.on_mount.map(ea).filter(le);e.$$.on_destroy?e.$$.on_destroy.push(...r):te(r),e.$$.on_mount=[]}),o.forEach(Qe)}function L(e,t){const n=e.$$;n.fragment!==null&&(ic(n.after_update),te(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function fc(e,t){e.$$.dirty[0]===-1&&(je.push(e),la(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function Q(e,t,n,i,s,o,r,a=[-1]){const l=Oe;Pe(e);const c=e.$$={fragment:null,ctx:[],props:o,update:ht,not_equal:s,bound:rs(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(l?l.$$.context:[])),callbacks:rs(),dirty:a,skip_bound:!1,root:t.target||l.$$.root};r&&r(c.root);let u=!1;if(c.ctx=n?n(e,t.props||{},(f,d,...h)=>{const g=h.length?h[0]:d;return c.ctx&&s(c.ctx[f],c.ctx[f]=g)&&(!c.skip_bound&&c.bound[f]&&c.bound[f](g),u&&fc(e,f)),d}):[],c.update(),u=!0,te(c.before_update),c.fragment=i?i(c.ctx):!1,t.target){if(t.hydrate){El();const f=jl(t.target);c.fragment&&c.fragment.l(f),f.forEach(U)}else c.fragment&&c.fragment.c();t.intro&&S(e.$$.fragment),E(e,t.target,t.anchor,t.customElement),Ll(),ua()}Pe(l)}class X{$destroy(){L(this,1),this.$destroy=ht}$on(t,n){if(!le(n))return ht;const i=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return i.push(n),()=>{const s=i.indexOf(n);s!==-1&&i.splice(s,1)}}$set(t){this.$$set&&!Pl(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const He=[];function cs(e,t){return{subscribe:Ot(e,t).subscribe}}function Ot(e,t=ht){let n;const i=new Set;function s(a){if(Y(e,a)&&(e=a,n)){const l=!He.length;for(const c of i)c[1](),He.push(c,e);if(l){for(let c=0;c<He.length;c+=2)He[c][0](He[c+1]);He.length=0}}}function o(a){s(a(e))}function r(a,l=ht){const c=[a,l];return i.add(c),i.size===1&&(n=t(s)||ht),a(e),()=>{i.delete(c),i.size===0&&n&&(n(),n=null)}}return{set:s,update:o,subscribe:r}}function Is(e,t,n){const i=!Array.isArray(e),s=i?[e]:e,o=t.length<2;return cs(n,r=>{let a=!1;const l=[];let c=0,u=ht;const f=()=>{if(c)return;u();const h=t(i?l[0]:l,r);o?r(h):u=le(h)?h:ht},d=s.map((h,g)=>Ds(h,p=>{l[g]=p,c&=~(1<<g),a&&f()},()=>{c|=1<<g}));return a=!0,f(),function(){te(d),u(),a=!1}})}function us(e,t=!1){return e=e.slice(e.startsWith("/#")?2:0,e.endsWith("/*")?-2:void 0),e.startsWith("/")||(e="/"+e),e==="/"&&(e=""),t&&!e.endsWith("/")&&(e+="/"),e}function dc(e,t){e=us(e,!0),t=us(t,!0);let n=[],i={},s=!0,o=e.split("/").map(a=>a.startsWith(":")?(n.push(a.slice(1)),"([^\\/]+)"):a).join("\\/"),r=t.match(new RegExp(`^${o}$`));return r||(s=!1,r=t.match(new RegExp(`^${o}`))),r?(n.forEach((a,l)=>i[a]=r[l+1]),{exact:s,params:i,part:r[0].slice(0,-1)}):null}function ha(e,t,n){if(n==="")return e;if(n[0]==="/")return n;let i=r=>r.split("/").filter(a=>a!==""),s=i(e);return"/"+(t?i(t):[]).map((r,a)=>s[a]).join("/")+"/"+n}function oo(e,t,n,i){let s=[t,"data-"+t].reduce((o,r)=>{let a=e.getAttribute(r);return n&&e.removeAttribute(r),a===null?o:a},!1);return!i&&s===""?!0:s||i||!1}function hc(e){let t=e.split("&").map(n=>n.split("=")).reduce((n,i)=>{let s=i[0];if(!s)return n;let o=i.length>1?i[i.length-1]:!0;return typeof o=="string"&&o.includes(",")&&(o=o.split(",")),n[s]===void 0?n[s]=[o]:n[s].push(o),n},{});return Object.entries(t).reduce((n,i)=>(n[i[0]]=i[1].length>1?i[1]:i[1][0],n),{})}function gc(e){return Object.entries(e).map(([t,n])=>n?n===!0?t:`${t}=${Array.isArray(n)?n.join(","):n}`:null).filter(t=>t).join("&")}function ro(e,t){return e?t+e:""}function ga(e){throw new Error("[Tinro] "+e)}var Nt={HISTORY:1,HASH:2,MEMORY:3,OFF:4,run(e,t,n,i){return e===this.HISTORY?t&&t():e===this.HASH?n&&n():i&&i()},getDefault(){return!window||window.location.pathname==="srcdoc"?this.MEMORY:this.HISTORY}},Vs,pa,ma,li="",Ht=pc();function pc(){let e=Nt.getDefault(),t,n=r=>window.onhashchange=window.onpopstate=Vs=null,i=r=>t&&t(Ni(e)),s=r=>{r&&(e=r),n(),e!==Nt.OFF&&Nt.run(e,a=>window.onpopstate=i,a=>window.onhashchange=i)&&i()},o=r=>{let a=Object.assign(Ni(e),r);return a.path+ro(gc(a.query),"?")+ro(a.hash,"#")};return{mode:s,get:r=>Ni(e),go(r,a){mc(e,r,a),i()},start(r){t=r,s()},stop(){t=null,s(Nt.OFF)},set(r){this.go(o(r),!r.path)},methods(){return _c(this)},base:r=>li=r}}function mc(e,t,n){!n&&(pa=ma);let i=s=>history[`${n?"replace":"push"}State`]({},"",s);Nt.run(e,s=>i(li+t),s=>i(`#${t}`),s=>Vs=t)}function Ni(e){let t=window.location,n=Nt.run(e,s=>(li?t.pathname.replace(li,""):t.pathname)+t.search+t.hash,s=>String(t.hash.slice(1)||"/"),s=>Vs||"/"),i=n.match(/^([^?#]+)(?:\?([^#]+))?(?:\#(.+))?$/);return ma=n,{url:n,from:pa,path:i[1]||"",query:hc(i[2]||""),hash:i[3]||""}}function _c(e){let t=()=>e.get().query,n=r=>e.set({query:r}),i=r=>n(r(t())),s=()=>e.get().hash,o=r=>e.set({hash:r});return{hash:{get:s,set:o,clear:()=>o("")},query:{replace:n,clear:()=>n(""),get(r){return r?t()[r]:t()},set(r,a){i(l=>(l[r]=a,l))},delete(r){i(a=>(a[r]&&delete a[r],a))}}}}var Ee=bc();function bc(){let{subscribe:e}=Ot(Ht.get(),t=>{Ht.start(t);let n=yc(Ht.go);return()=>{Ht.stop(),n()}});return{subscribe:e,goto:Ht.go,params:vc,meta:zs,useHashNavigation:t=>Ht.mode(t?Nt.HASH:Nt.HISTORY),mode:{hash:()=>Ht.mode(Nt.HASH),history:()=>Ht.mode(Nt.HISTORY),memory:()=>Ht.mode(Nt.MEMORY)},base:Ht.base,location:Ht.methods()}}function yc(e){let t=n=>{let i=n.target.closest("a[href]"),s=i&&oo(i,"target",!1,"_self"),o=i&&oo(i,"tinro-ignore"),r=n.ctrlKey||n.metaKey||n.altKey||n.shiftKey;if(s=="_self"&&!o&&!r&&i){let a=i.getAttribute("href").replace(/^\/#/,"");/^\/\/|^#|^[a-zA-Z]+:/.test(a)||(n.preventDefault(),e(a.startsWith("/")?a:i.href.replace(window.location.origin,"")))}};return addEventListener("click",t),()=>removeEventListener("click",t)}function vc(){return Ls("tinro").meta.params}var ci="tinro",xc=_a({pattern:"",matched:!0});function wc(e){let t=Ls(ci)||xc;(t.exact||t.fallback)&&ga(`${e.fallback?"<Route fallback>":`<Route path="${e.path}">`}  can't be inside ${t.fallback?"<Route fallback>":`<Route path="${t.path||"/"}"> with exact path`}`);let n=e.fallback?"fallbacks":"childs",i=Ot({}),s=_a({fallback:e.fallback,parent:t,update(o){s.exact=!o.path.endsWith("/*"),s.pattern=us(`${s.parent.pattern||""}${o.path}`),s.redirect=o.redirect,s.firstmatch=o.firstmatch,s.breadcrumb=o.breadcrumb,s.match()},register:()=>(s.parent[n].add(s),async()=>{s.parent[n].delete(s),s.parent.activeChilds.delete(s),s.router.un&&s.router.un(),s.parent.match()}),show:()=>{e.onShow(),!s.fallback&&s.parent.activeChilds.add(s)},hide:()=>{e.onHide(),s.parent.activeChilds.delete(s)},match:async()=>{s.matched=!1;let{path:o,url:r,from:a,query:l}=s.router.location,c=dc(s.pattern,o);if(!s.fallback&&c&&s.redirect&&(!s.exact||s.exact&&c.exact)){let u=ha(o,s.parent.pattern,s.redirect);return Ee.goto(u,!0)}s.meta=c&&{from:a,url:r,query:l,match:c.part,pattern:s.pattern,breadcrumbs:s.parent.meta&&s.parent.meta.breadcrumbs.slice()||[],params:c.params,subscribe:i.subscribe},s.breadcrumb&&s.meta&&s.meta.breadcrumbs.push({name:s.breadcrumb,path:c.part}),i.set(s.meta),c&&!s.fallback&&(!s.exact||s.exact&&c.exact)&&(!s.parent.firstmatch||!s.parent.matched)?(e.onMeta(s.meta),s.parent.matched=!0,s.show()):s.hide(),c&&s.showFallbacks()}});return Jl(ci,s),Ai(()=>s.register()),s}function zs(){return tc(ci)?Ls(ci).meta:ga("meta() function must be run inside any `<Route>` child component only")}function _a(e){let t={router:{},exact:!1,pattern:null,meta:null,parent:null,fallback:!1,redirect:!1,firstmatch:!1,breadcrumb:null,matched:!1,childs:new Set,activeChilds:new Set,fallbacks:new Set,async showFallbacks(){if(!this.fallback&&(await Ts(),this.childs.size>0&&this.activeChilds.size==0||this.childs.size==0&&this.fallbacks.size>0)){let n=this;for(;n.fallbacks.size==0;)if(n=n.parent,!n)return;n&&n.fallbacks.forEach(i=>{if(i.redirect){let s=ha("/",i.parent.pattern,i.redirect);Ee.goto(s,!0)}else i.show()})}},start(){this.router.un||(this.router.un=Ee.subscribe(n=>{this.router.location=n,this.pattern!==null&&this.match()}))},match(){this.showFallbacks()}};return Object.assign(t,e),t.start(),t}const Sc=e=>({params:e&2,meta:e&4}),ao=e=>({params:e[1],meta:e[2]});function lo(e){let t;const n=e[9].default,i=At(n,e,e[8],ao);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&262)&&Ft(i,n,s,s[8],t?Pt(n,s[8],o,Sc):Dt(s[8]),ao)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Cc(e){let t,n,i=e[0]&&lo(e);return{c(){i&&i.c(),t=kn()},m(s,o){i&&i.m(s,o),it(s,t,o),n=!0},p(s,[o]){s[0]?i?(i.p(s,o),o&1&&S(i,1)):(i=lo(s),i.c(),S(i,1),i.m(t.parentNode,t)):i&&(kt(),C(i,1,1,()=>{i=null}),Rt())},i(s){n||(S(i),n=!0)},o(s){C(i),n=!1},d(s){i&&i.d(s),s&&U(t)}}}function Mc(e,t,n){let{$$slots:i={},$$scope:s}=t,{path:o="/*"}=t,{fallback:r=!1}=t,{redirect:a=!1}=t,{firstmatch:l=!1}=t,{breadcrumb:c=null}=t,u=!1,f={},d={};const h=wc({fallback:r,onShow(){n(0,u=!0)},onHide(){n(0,u=!1)},onMeta(g){n(2,d=g),n(1,f=d.params)}});return e.$$set=g=>{"path"in g&&n(3,o=g.path),"fallback"in g&&n(4,r=g.fallback),"redirect"in g&&n(5,a=g.redirect),"firstmatch"in g&&n(6,l=g.firstmatch),"breadcrumb"in g&&n(7,c=g.breadcrumb),"$$scope"in g&&n(8,s=g.$$scope)},e.$$.update=()=>{e.$$.dirty&232&&h.update({path:o,redirect:a,firstmatch:l,breadcrumb:c})},[u,f,d,o,r,a,l,c,s,i]}class co extends X{constructor(t){super(),Q(this,t,Mc,Cc,Y,{path:3,fallback:4,redirect:5,firstmatch:6,breadcrumb:7})}}async function ba(e,t={}){const n={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)},i=window.GetParentResourceName?window.GetParentResourceName():"nui-frame-app";return await(await fetch(`https://${i}/${e}`,n)).json()}var uo={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};function fo(e,t,n){const i=e.slice();return i[9]=t[n][0],i[10]=t[n][1],i}function Bi(e){let t,n=[e[10]],i={};for(let s=0;s<n.length;s+=1)i=N(i,n[s]);return{c(){t=ra(e[9]),ii(t,i)},m(s,o){it(s,t,o)},p(s,o){ii(t,i=zt(n,[o&16&&s[10]]))},d(s){s&&U(t)}}}function ho(e){let t=e[9],n,i=e[9]&&Bi(e);return{c(){i&&i.c(),n=kn()},m(s,o){i&&i.m(s,o),it(s,n,o)},p(s,o){s[9]?t?Y(t,s[9])?(i.d(1),i=Bi(s),t=s[9],i.c(),i.m(n.parentNode,n)):i.p(s,o):(i=Bi(s),t=s[9],i.c(),i.m(n.parentNode,n)):t&&(i.d(1),i=null,t=s[9])},d(s){s&&U(n),i&&i.d(s)}}}function kc(e){let t,n,i,s,o=e[4],r=[];for(let f=0;f<o.length;f+=1)r[f]=ho(fo(e,o,f));const a=e[8].default,l=At(a,e,e[7],null);let c=[uo,e[5],{width:e[2]},{height:e[2]},{stroke:e[1]},{"stroke-width":e[3]},{class:i=`tabler-icon tabler-icon-${e[0]} ${e[6].class??""}`}],u={};for(let f=0;f<c.length;f+=1)u=N(u,c[f]);return{c(){t=ra("svg");for(let f=0;f<r.length;f+=1)r[f].c();n=kn(),l&&l.c(),ii(t,u)},m(f,d){it(f,t,d);for(let h=0;h<r.length;h+=1)r[h]&&r[h].m(t,null);w(t,n),l&&l.m(t,null),s=!0},p(f,[d]){if(d&16){o=f[4];let h;for(h=0;h<o.length;h+=1){const g=fo(f,o,h);r[h]?r[h].p(g,d):(r[h]=ho(g),r[h].c(),r[h].m(t,n))}for(;h<r.length;h+=1)r[h].d(1);r.length=o.length}l&&l.p&&(!s||d&128)&&Ft(l,a,f,f[7],s?Pt(a,f[7],d,null):Dt(f[7]),null),ii(t,u=zt(c,[uo,d&32&&f[5],(!s||d&4)&&{width:f[2]},(!s||d&4)&&{height:f[2]},(!s||d&2)&&{stroke:f[1]},(!s||d&8)&&{"stroke-width":f[3]},(!s||d&65&&i!==(i=`tabler-icon tabler-icon-${f[0]} ${f[6].class??""}`))&&{class:i}]))},i(f){s||(S(l,f),s=!0)},o(f){C(l,f),s=!1},d(f){f&&U(t),Xe(r,f),l&&l.d(f)}}}function Rc(e,t,n){const i=["name","color","size","stroke","iconNode"];let s=io(t,i),{$$slots:o={},$$scope:r}=t,{name:a}=t,{color:l="currentColor"}=t,{size:c=24}=t,{stroke:u=2}=t,{iconNode:f}=t;return e.$$set=d=>{n(6,t=N(N({},t),nt(d))),n(5,s=io(t,i)),"name"in d&&n(0,a=d.name),"color"in d&&n(1,l=d.color),"size"in d&&n(2,c=d.size),"stroke"in d&&n(3,u=d.stroke),"iconNode"in d&&n(4,f=d.iconNode),"$$scope"in d&&n(7,r=d.$$scope)},t=nt(t),[a,l,c,u,f,s,t,r,o]}class $c extends X{constructor(t){super(),Q(this,t,Rc,kc,Y,{name:0,color:1,size:2,stroke:3,iconNode:4})}}const ce=$c;function Ac(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Pc(e){let t,n;const i=[{name:"chevron-left"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Ac]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Fc(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M15 6l-6 6l6 6"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class Dc extends X{constructor(t){super(),Q(this,t,Fc,Pc,Y,{})}}const ya=Dc;function Oc(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Ec(e){let t,n;const i=[{name:"chevron-right"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Oc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Lc(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M9 6l6 6l-6 6"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class Tc extends X{constructor(t){super(),Q(this,t,Lc,Ec,Y,{})}}const Ic=Tc;function Vc(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function zc(e){let t,n;const i=[{name:"chevrons-left"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Vc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Hc(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M11 7l-5 5l5 5"}],["path",{d:"M17 7l-5 5l5 5"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class Nc extends X{constructor(t){super(),Q(this,t,Hc,zc,Y,{})}}const Bc=Nc;function jc(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Wc(e){let t,n;const i=[{name:"chevrons-right"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[jc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Gc(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M7 7l5 5l-5 5"}],["path",{d:"M13 7l5 5l-5 5"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class qc extends X{constructor(t){super(),Q(this,t,Gc,Wc,Y,{})}}const Yc=qc,ji=Ot(!1),fs=Ot("");let go;const Uc=Is(fs,(e,t)=>(go=setTimeout(()=>t(e),500),()=>clearTimeout(go))),ds=Ot([]),Xc=Is([ds,Uc],([e,t],n)=>{if(t===""||!t)return n(e);const i=t.toLowerCase();return n(e.filter(s=>s.toLowerCase().includes(i)))}),hs=Ot({queries:0,timeQuerying:0,slowQueries:0}),gs=Ot({labels:[],data:[]}),Qn=Ot([]),ps=Ot({resourceQueriesCount:0,resourceSlowQueries:0,resourceTime:0}),Bt=Ot({search:"",page:0});function Kc(e){let t,n,i,s,o,r,a,l,c,u,f,d=e[1].page+1+"",h,g,p,m,_,b,y,x,v,R,k,$,P,F;return i=new Bc({}),a=new ya({}),b=new Ic({}),R=new Yc({}),{c(){t=A("div"),n=A("button"),V(i.$$.fragment),o=q(),r=A("button"),V(a.$$.fragment),c=q(),u=A("p"),f=J("Page "),h=J(d),g=J(" of "),p=J(e[0]),m=q(),_=A("button"),V(b.$$.fragment),x=q(),v=A("button"),V(R.$$.fragment),n.disabled=s=e[1].page===0,D(n,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),r.disabled=l=e[1].page===0,D(r,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),_.disabled=y=e[1].page>=e[0]-1,D(_,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),v.disabled=k=e[1].page===e[0]-1,D(v,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),D(t,"class","flex items-center justify-center gap-6 pb-5")},m(M,O){it(M,t,O),w(t,n),E(i,n,null),w(t,o),w(t,r),E(a,r,null),w(t,c),w(t,u),w(u,f),w(u,h),w(u,g),w(u,p),w(t,m),w(t,_),E(b,_,null),w(t,x),w(t,v),E(R,v,null),$=!0,P||(F=[It(n,"click",e[2]),It(r,"click",e[3]),It(_,"click",e[4]),It(v,"click",e[5])],P=!0)},p(M,[O]){(!$||O&2&&s!==(s=M[1].page===0))&&(n.disabled=s),(!$||O&2&&l!==(l=M[1].page===0))&&(r.disabled=l),(!$||O&2)&&d!==(d=M[1].page+1+"")&&Wt(h,d),(!$||O&1)&&Wt(p,M[0]),(!$||O&3&&y!==(y=M[1].page>=M[0]-1))&&(_.disabled=y),(!$||O&3&&k!==(k=M[1].page===M[0]-1))&&(v.disabled=k)},i(M){$||(S(i.$$.fragment,M),S(a.$$.fragment,M),S(b.$$.fragment,M),S(R.$$.fragment,M),$=!0)},o(M){C(i.$$.fragment,M),C(a.$$.fragment,M),C(b.$$.fragment,M),C(R.$$.fragment,M),$=!1},d(M){M&&U(t),L(i),L(a),L(b),L(R),P=!1,te(F)}}}function Qc(e,t,n){let i;gt(e,Bt,c=>n(1,i=c));let{maxPage:s}=t;en(()=>n(0,s=0));const o=()=>bt(Bt,i.page=0,i),r=()=>bt(Bt,i.page--,i),a=()=>bt(Bt,i.page++,i),l=()=>bt(Bt,i.page=s-1,i);return e.$$set=c=>{"maxPage"in c&&n(0,s=c.maxPage)},[s,i,o,r,a,l]}let Zc=class extends X{constructor(t){super(),Q(this,t,Qc,Kc,Y,{maxPage:0})}};function Jc(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function tu(e){let t,n;const i=[{name:"chevron-down"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Jc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function eu(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M6 9l6 6l6 -6"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class nu extends X{constructor(t){super(),Q(this,t,eu,tu,Y,{})}}const iu=nu;function su(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function ou(e){let t,n;const i=[{name:"chevron-up"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[su]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function ru(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M6 15l6 -6l6 6"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class au extends X{constructor(t){super(),Q(this,t,ru,ou,Y,{})}}const lu=au;/**
 * table-core
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function he(e,t){return typeof e=="function"?e(t):e}function Vt(e,t){return n=>{t.setState(i=>({...i,[e]:he(n,i[e])}))}}function ui(e){return e instanceof Function}function cu(e,t){const n=[],i=s=>{s.forEach(o=>{n.push(o);const r=t(o);r!=null&&r.length&&i(r)})};return i(e),n}function I(e,t,n){let i=[],s;return()=>{let o;n.key&&n.debug&&(o=Date.now());const r=e();if(!(r.length!==i.length||r.some((c,u)=>i[u]!==c)))return s;i=r;let l;if(n.key&&n.debug&&(l=Date.now()),s=t(...r),n==null||n.onChange==null||n.onChange(s),n.key&&n.debug&&n!=null&&n.debug()){const c=Math.round((Date.now()-o)*100)/100,u=Math.round((Date.now()-l)*100)/100,f=u/16,d=(h,g)=>{for(h=String(h);h.length<g;)h=" "+h;return h};console.info(`%c⏱ ${d(u,5)} /${d(c,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*f,120))}deg 100% 31%);`,n==null?void 0:n.key)}return s}}function uu(e,t,n,i){var s,o;const a={...e._getDefaultColumnDef(),...t},l=a.accessorKey;let c=(s=(o=a.id)!=null?o:l?l.replace(".","_"):void 0)!=null?s:typeof a.header=="string"?a.header:void 0,u;if(a.accessorFn?u=a.accessorFn:l&&(l.includes(".")?u=d=>{let h=d;for(const p of l.split(".")){var g;h=(g=h)==null?void 0:g[p]}return h}:u=d=>d[a.accessorKey]),!c)throw new Error;let f={id:`${String(c)}`,accessorFn:u,parent:i,depth:n,columnDef:a,columns:[],getFlatColumns:I(()=>[!0],()=>{var d;return[f,...(d=f.columns)==null?void 0:d.flatMap(h=>h.getFlatColumns())]},{key:"column.getFlatColumns",debug:()=>{var d;return(d=e.options.debugAll)!=null?d:e.options.debugColumns}}),getLeafColumns:I(()=>[e._getOrderColumnsFn()],d=>{var h;if((h=f.columns)!=null&&h.length){let g=f.columns.flatMap(p=>p.getLeafColumns());return d(g)}return[f]},{key:"column.getLeafColumns",debug:()=>{var d;return(d=e.options.debugAll)!=null?d:e.options.debugColumns}})};return f=e._features.reduce((d,h)=>Object.assign(d,h.createColumn==null?void 0:h.createColumn(f,e)),f),f}function po(e,t,n){var i;let o={id:(i=n.id)!=null?i:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const r=[],a=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(a),r.push(l)};return a(o),r},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(r=>{Object.assign(o,r.createHeader==null?void 0:r.createHeader(o,e))}),o}const fu={createTable:e=>({getHeaderGroups:I(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,i,s)=>{var o,r;const a=(o=i==null?void 0:i.map(f=>n.find(d=>d.id===f)).filter(Boolean))!=null?o:[],l=(r=s==null?void 0:s.map(f=>n.find(d=>d.id===f)).filter(Boolean))!=null?r:[],c=n.filter(f=>!(i!=null&&i.includes(f.id))&&!(s!=null&&s.includes(f.id)));return Dn(t,[...a,...c,...l],e)},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterHeaderGroups:I(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,i,s)=>(n=n.filter(o=>!(i!=null&&i.includes(o.id))&&!(s!=null&&s.includes(o.id))),Dn(t,n,e,"center")),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftHeaderGroups:I(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,i)=>{var s;const o=(s=i==null?void 0:i.map(r=>n.find(a=>a.id===r)).filter(Boolean))!=null?s:[];return Dn(t,o,e,"left")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightHeaderGroups:I(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,i)=>{var s;const o=(s=i==null?void 0:i.map(r=>n.find(a=>a.id===r)).filter(Boolean))!=null?s:[];return Dn(t,o,e,"right")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getFooterGroups:I(()=>[e.getHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftFooterGroups:I(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterFooterGroups:I(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightFooterGroups:I(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getFlatHeaders:I(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftFlatHeaders:I(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterFlatHeaders:I(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightFlatHeaders:I(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterLeafHeaders:I(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var i;return!((i=n.subHeaders)!=null&&i.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftLeafHeaders:I(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var i;return!((i=n.subHeaders)!=null&&i.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightLeafHeaders:I(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var i;return!((i=n.subHeaders)!=null&&i.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeafHeaders:I(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,i)=>{var s,o,r,a,l,c;return[...(s=(o=t[0])==null?void 0:o.headers)!=null?s:[],...(r=(a=n[0])==null?void 0:a.headers)!=null?r:[],...(l=(c=i[0])==null?void 0:c.headers)!=null?l:[]].map(u=>u.getLeafHeaders()).flat()},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}})})};function Dn(e,t,n,i){var s,o;let r=0;const a=function(d,h){h===void 0&&(h=1),r=Math.max(r,h),d.filter(g=>g.getIsVisible()).forEach(g=>{var p;(p=g.columns)!=null&&p.length&&a(g.columns,h+1)},0)};a(e);let l=[];const c=(d,h)=>{const g={depth:h,id:[i,`${h}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(m=>{const _=[...p].reverse()[0],b=m.column.depth===g.depth;let y,x=!1;if(b&&m.column.parent?y=m.column.parent:(y=m.column,x=!0),_&&(_==null?void 0:_.column)===y)_.subHeaders.push(m);else{const v=po(n,y,{id:[i,h,y.id,m==null?void 0:m.id].filter(Boolean).join("_"),isPlaceholder:x,placeholderId:x?`${p.filter(R=>R.column===y).length}`:void 0,depth:h,index:p.length});v.subHeaders.push(m),p.push(v)}g.headers.push(m),m.headerGroup=g}),l.push(g),h>0&&c(p,h-1)},u=t.map((d,h)=>po(n,d,{depth:r,index:h}));c(u,r-1),l.reverse();const f=d=>d.filter(g=>g.column.getIsVisible()).map(g=>{let p=0,m=0,_=[0];g.subHeaders&&g.subHeaders.length?(_=[],f(g.subHeaders).forEach(y=>{let{colSpan:x,rowSpan:v}=y;p+=x,_.push(v)})):p=1;const b=Math.min(..._);return m=m+b,g.colSpan=p,g.rowSpan=m,{colSpan:p,rowSpan:m}});return f((s=(o=l[0])==null?void 0:o.headers)!=null?s:[]),l}const On={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},Wi=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),du={getDefaultColumnDef:()=>On,getInitialState:e=>({columnSizing:{},columnSizingInfo:Wi(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",onColumnSizingChange:Vt("columnSizing",e),onColumnSizingInfoChange:Vt("columnSizingInfo",e)}),createColumn:(e,t)=>({getSize:()=>{var n,i,s;const o=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:On.minSize,(i=o??e.columnDef.size)!=null?i:On.size),(s=e.columnDef.maxSize)!=null?s:On.maxSize)},getStart:n=>{const i=n?n==="left"?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns(),s=i.findIndex(o=>o.id===e.id);if(s>0){const o=i[s-1];return o.getStart(n)+o.getSize()}return 0},resetSize:()=>{t.setColumnSizing(n=>{let{[e.id]:i,...s}=n;return s})},getCanResize:()=>{var n,i;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((i=t.options.enableColumnResizing)!=null?i:!0)},getIsResizing:()=>t.getState().columnSizingInfo.isResizingColumn===e.id}),createHeader:(e,t)=>({getSize:()=>{let n=0;const i=s=>{if(s.subHeaders.length)s.subHeaders.forEach(i);else{var o;n+=(o=s.column.getSize())!=null?o:0}};return i(e),n},getStart:()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},getResizeHandler:()=>{const n=t.getColumn(e.column.id),i=n==null?void 0:n.getCanResize();return s=>{if(!n||!i||(s.persist==null||s.persist(),Gi(s)&&s.touches&&s.touches.length>1))return;const o=e.getSize(),r=e?e.getLeafHeaders().map(p=>[p.column.id,p.column.getSize()]):[[n.id,n.getSize()]],a=Gi(s)?Math.round(s.touches[0].clientX):s.clientX,l={},c=(p,m)=>{typeof m=="number"&&(t.setColumnSizingInfo(_=>{var b,y;const x=m-((b=_==null?void 0:_.startOffset)!=null?b:0),v=Math.max(x/((y=_==null?void 0:_.startSize)!=null?y:0),-.999999);return _.columnSizingStart.forEach(R=>{let[k,$]=R;l[k]=Math.round(Math.max($+$*v,0)*100)/100}),{..._,deltaOffset:x,deltaPercentage:v}}),(t.options.columnResizeMode==="onChange"||p==="end")&&t.setColumnSizing(_=>({..._,...l})))},u=p=>c("move",p),f=p=>{c("end",p),t.setColumnSizingInfo(m=>({...m,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},d={moveHandler:p=>u(p.clientX),upHandler:p=>{document.removeEventListener("mousemove",d.moveHandler),document.removeEventListener("mouseup",d.upHandler),f(p.clientX)}},h={moveHandler:p=>(p.cancelable&&(p.preventDefault(),p.stopPropagation()),u(p.touches[0].clientX),!1),upHandler:p=>{var m;document.removeEventListener("touchmove",h.moveHandler),document.removeEventListener("touchend",h.upHandler),p.cancelable&&(p.preventDefault(),p.stopPropagation()),f((m=p.touches[0])==null?void 0:m.clientX)}},g=hu()?{passive:!1}:!1;Gi(s)?(document.addEventListener("touchmove",h.moveHandler,g),document.addEventListener("touchend",h.upHandler,g)):(document.addEventListener("mousemove",d.moveHandler,g),document.addEventListener("mouseup",d.upHandler,g)),t.setColumnSizingInfo(p=>({...p,startOffset:a,startSize:o,deltaOffset:0,deltaPercentage:0,columnSizingStart:r,isResizingColumn:n.id}))}}}),createTable:e=>({setColumnSizing:t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),setColumnSizingInfo:t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),resetColumnSizing:t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},resetHeaderSizeInfo:t=>{var n;e.setColumnSizingInfo(t?Wi():(n=e.initialState.columnSizingInfo)!=null?n:Wi())},getTotalSize:()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0},getLeftTotalSize:()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0},getCenterTotalSize:()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0},getRightTotalSize:()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0}})};let En=null;function hu(){if(typeof En=="boolean")return En;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return En=e,En}function Gi(e){return e.type==="touchstart"}const gu={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Vt("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;return{_autoResetExpanded:()=>{var i,s;if(!t){e._queue(()=>{t=!0});return}if((i=(s=e.options.autoResetAll)!=null?s:e.options.autoResetExpanded)!=null?i:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},setExpanded:i=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(i),toggleAllRowsExpanded:i=>{i??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},resetExpanded:i=>{var s,o;e.setExpanded(i?{}:(s=(o=e.initialState)==null?void 0:o.expanded)!=null?s:{})},getCanSomeRowsExpand:()=>e.getRowModel().flatRows.some(i=>i.getCanExpand()),getToggleAllRowsExpandedHandler:()=>i=>{i.persist==null||i.persist(),e.toggleAllRowsExpanded()},getIsSomeRowsExpanded:()=>{const i=e.getState().expanded;return i===!0||Object.values(i).some(Boolean)},getIsAllRowsExpanded:()=>{const i=e.getState().expanded;return typeof i=="boolean"?i===!0:!(!Object.keys(i).length||e.getRowModel().flatRows.some(s=>!s.getIsExpanded()))},getExpandedDepth:()=>{let i=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(o=>{const r=o.split(".");i=Math.max(i,r.length)}),i},getPreExpandedRowModel:()=>e.getSortedRowModel(),getExpandedRowModel:()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())}},createRow:(e,t)=>({toggleExpanded:n=>{t.setExpanded(i=>{var s;const o=i===!0?!0:!!(i!=null&&i[e.id]);let r={};if(i===!0?Object.keys(t.getRowModel().rowsById).forEach(a=>{r[a]=!0}):r=i,n=(s=n)!=null?s:!o,!o&&n)return{...r,[e.id]:!0};if(o&&!n){const{[e.id]:a,...l}=r;return l}return i})},getIsExpanded:()=>{var n;const i=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:i===!0||i!=null&&i[e.id])},getCanExpand:()=>{var n,i,s;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((i=t.options.enableExpanding)!=null?i:!0)&&!!((s=e.subRows)!=null&&s.length)},getToggleExpandedHandler:()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}})},va=(e,t,n)=>{var i;const s=n.toLowerCase();return!!((i=e.getValue(t))!=null&&i.toLowerCase().includes(s))};va.autoRemove=e=>Gt(e);const xa=(e,t,n)=>{var i;return!!((i=e.getValue(t))!=null&&i.includes(n))};xa.autoRemove=e=>Gt(e);const wa=(e,t,n)=>{var i;return((i=e.getValue(t))==null?void 0:i.toLowerCase())===n.toLowerCase()};wa.autoRemove=e=>Gt(e);const Sa=(e,t,n)=>{var i;return(i=e.getValue(t))==null?void 0:i.includes(n)};Sa.autoRemove=e=>Gt(e)||!(e!=null&&e.length);const Ca=(e,t,n)=>!n.some(i=>{var s;return!((s=e.getValue(t))!=null&&s.includes(i))});Ca.autoRemove=e=>Gt(e)||!(e!=null&&e.length);const Ma=(e,t,n)=>n.some(i=>{var s;return(s=e.getValue(t))==null?void 0:s.includes(i)});Ma.autoRemove=e=>Gt(e)||!(e!=null&&e.length);const ka=(e,t,n)=>e.getValue(t)===n;ka.autoRemove=e=>Gt(e);const Ra=(e,t,n)=>e.getValue(t)==n;Ra.autoRemove=e=>Gt(e);const Hs=(e,t,n)=>{let[i,s]=n;const o=e.getValue(t);return o>=i&&o<=s};Hs.resolveFilterValue=e=>{let[t,n]=e,i=typeof t!="number"?parseFloat(t):t,s=typeof n!="number"?parseFloat(n):n,o=t===null||Number.isNaN(i)?-1/0:i,r=n===null||Number.isNaN(s)?1/0:s;if(o>r){const a=o;o=r,r=a}return[o,r]};Hs.autoRemove=e=>Gt(e)||Gt(e[0])&&Gt(e[1]);const ne={includesString:va,includesStringSensitive:xa,equalsString:wa,arrIncludes:Sa,arrIncludesAll:Ca,arrIncludesSome:Ma,equals:ka,weakEquals:Ra,inNumberRange:Hs};function Gt(e){return e==null||e===""}const pu={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],globalFilter:void 0,...e}),getDefaultOptions:e=>({onColumnFiltersChange:Vt("columnFilters",e),onGlobalFilterChange:Vt("globalFilter",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100,globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n,i;const s=(n=e.getCoreRowModel().flatRows[0])==null||(i=n._getAllCellsByColumnId()[t.id])==null?void 0:i.getValue();return typeof s=="string"||typeof s=="number"}}),createColumn:(e,t)=>({getAutoFilterFn:()=>{const n=t.getCoreRowModel().flatRows[0],i=n==null?void 0:n.getValue(e.id);return typeof i=="string"?ne.includesString:typeof i=="number"?ne.inNumberRange:typeof i=="boolean"||i!==null&&typeof i=="object"?ne.equals:Array.isArray(i)?ne.arrIncludes:ne.weakEquals},getFilterFn:()=>{var n,i;return ui(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(i=t.options.filterFns)==null?void 0:i[e.columnDef.filterFn])!=null?n:ne[e.columnDef.filterFn]},getCanFilter:()=>{var n,i,s;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((i=t.options.enableColumnFilters)!=null?i:!0)&&((s=t.options.enableFilters)!=null?s:!0)&&!!e.accessorFn},getCanGlobalFilter:()=>{var n,i,s,o;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((i=t.options.enableGlobalFilter)!=null?i:!0)&&((s=t.options.enableFilters)!=null?s:!0)&&((o=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?o:!0)&&!!e.accessorFn},getIsFiltered:()=>e.getFilterIndex()>-1,getFilterValue:()=>{var n,i;return(n=t.getState().columnFilters)==null||(i=n.find(s=>s.id===e.id))==null?void 0:i.value},getFilterIndex:()=>{var n,i;return(n=(i=t.getState().columnFilters)==null?void 0:i.findIndex(s=>s.id===e.id))!=null?n:-1},setFilterValue:n=>{t.setColumnFilters(i=>{const s=e.getFilterFn(),o=i==null?void 0:i.find(u=>u.id===e.id),r=he(n,o?o.value:void 0);if(mo(s,r,e)){var a;return(a=i==null?void 0:i.filter(u=>u.id!==e.id))!=null?a:[]}const l={id:e.id,value:r};if(o){var c;return(c=i==null?void 0:i.map(u=>u.id===e.id?l:u))!=null?c:[]}return i!=null&&i.length?[...i,l]:[l]})},_getFacetedRowModel:t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),getFacetedRowModel:()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),_getFacetedUniqueValues:t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),getFacetedUniqueValues:()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,_getFacetedMinMaxValues:t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),getFacetedMinMaxValues:()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}),createRow:(e,t)=>({columnFilters:{},columnFiltersMeta:{}}),createTable:e=>({getGlobalAutoFilterFn:()=>ne.includesString,getGlobalFilterFn:()=>{var t,n;const{globalFilterFn:i}=e.options;return ui(i)?i:i==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[i])!=null?t:ne[i]},setColumnFilters:t=>{const n=e.getAllLeafColumns(),i=s=>{var o;return(o=he(t,s))==null?void 0:o.filter(r=>{const a=n.find(l=>l.id===r.id);if(a){const l=a.getFilterFn();if(mo(l,r.value,a))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(i)},setGlobalFilter:t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},resetGlobalFilter:t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)},resetColumnFilters:t=>{var n,i;e.setColumnFilters(t?[]:(n=(i=e.initialState)==null?void 0:i.columnFilters)!=null?n:[])},getPreFilteredRowModel:()=>e.getCoreRowModel(),getFilteredRowModel:()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel()),_getGlobalFacetedRowModel:e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),getGlobalFacetedRowModel:()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),_getGlobalFacetedUniqueValues:e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),getGlobalFacetedUniqueValues:()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,_getGlobalFacetedMinMaxValues:e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),getGlobalFacetedMinMaxValues:()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}})};function mo(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const mu=(e,t,n)=>n.reduce((i,s)=>{const o=s.getValue(e);return i+(typeof o=="number"?o:0)},0),_u=(e,t,n)=>{let i;return n.forEach(s=>{const o=s.getValue(e);o!=null&&(i>o||i===void 0&&o>=o)&&(i=o)}),i},bu=(e,t,n)=>{let i;return n.forEach(s=>{const o=s.getValue(e);o!=null&&(i<o||i===void 0&&o>=o)&&(i=o)}),i},yu=(e,t,n)=>{let i,s;return n.forEach(o=>{const r=o.getValue(e);r!=null&&(i===void 0?r>=r&&(i=s=r):(i>r&&(i=r),s<r&&(s=r)))}),[i,s]},vu=(e,t)=>{let n=0,i=0;if(t.forEach(s=>{let o=s.getValue(e);o!=null&&(o=+o)>=o&&(++n,i+=o)}),n)return i/n},xu=(e,t)=>{if(!t.length)return;let n=0,i=0;return t.forEach(s=>{let o=s.getValue(e);typeof o=="number"&&(n=Math.min(n,o),i=Math.max(i,o))}),(n+i)/2},wu=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),Su=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,Cu=(e,t)=>t.length,qi={sum:mu,min:_u,max:bu,extent:yu,mean:vu,median:xu,unique:wu,uniqueCount:Su,count:Cu},Mu={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Vt("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>({toggleGrouping:()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(i=>i!==e.id):[...n??[],e.id])},getCanGroup:()=>{var n,i,s,o;return(n=(i=(s=(o=e.columnDef.enableGrouping)!=null?o:!0)!=null?s:t.options.enableGrouping)!=null?i:!0)!=null?n:!!e.accessorFn},getIsGrouped:()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},getGroupedIndex:()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},getToggleGroupingHandler:()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},getAutoAggregationFn:()=>{const n=t.getCoreRowModel().flatRows[0],i=n==null?void 0:n.getValue(e.id);if(typeof i=="number")return qi.sum;if(Object.prototype.toString.call(i)==="[object Date]")return qi.extent},getAggregationFn:()=>{var n,i;if(!e)throw new Error;return ui(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(i=t.options.aggregationFns)==null?void 0:i[e.columnDef.aggregationFn])!=null?n:qi[e.columnDef.aggregationFn]}}),createTable:e=>({setGrouping:t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),resetGrouping:t=>{var n,i;e.setGrouping(t?[]:(n=(i=e.initialState)==null?void 0:i.grouping)!=null?n:[])},getPreGroupedRowModel:()=>e.getFilteredRowModel(),getGroupedRowModel:()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())}),createRow:e=>({getIsGrouped:()=>!!e.groupingColumnId,_groupingValuesCache:{}}),createCell:(e,t,n,i)=>({getIsGrouped:()=>t.getIsGrouped()&&t.id===n.groupingColumnId,getIsPlaceholder:()=>!e.getIsGrouped()&&t.getIsGrouped(),getIsAggregated:()=>{var s;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((s=n.subRows)!=null&&s.length)}})};function ku(e,t,n){if(!(t!=null&&t.length)||!n)return e;const i=e.filter(o=>!t.includes(o.id));return n==="remove"?i:[...t.map(o=>e.find(r=>r.id===o)).filter(Boolean),...i]}const Ru={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Vt("columnOrder",e)}),createTable:e=>({setColumnOrder:t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),resetColumnOrder:t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},_getOrderColumnsFn:I(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,i)=>s=>{let o=[];if(!(t!=null&&t.length))o=s;else{const r=[...t],a=[...s];for(;a.length&&r.length;){const l=r.shift(),c=a.findIndex(u=>u.id===l);c>-1&&o.push(a.splice(c,1)[0])}o=[...o,...a]}return ku(o,n,i)},{key:!1})})},ms=0,_s=10,Yi=()=>({pageIndex:ms,pageSize:_s}),$u={getInitialState:e=>({...e,pagination:{...Yi(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Vt("pagination",e)}),createTable:e=>{let t=!1,n=!1;return{_autoResetPageIndex:()=>{var i,s;if(!t){e._queue(()=>{t=!0});return}if((i=(s=e.options.autoResetAll)!=null?s:e.options.autoResetPageIndex)!=null?i:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},setPagination:i=>{const s=o=>he(i,o);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(s)},resetPagination:i=>{var s;e.setPagination(i?Yi():(s=e.initialState.pagination)!=null?s:Yi())},setPageIndex:i=>{e.setPagination(s=>{let o=he(i,s.pageIndex);const r=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return o=Math.max(0,Math.min(o,r)),{...s,pageIndex:o}})},resetPageIndex:i=>{var s,o,r;e.setPageIndex(i?ms:(s=(o=e.initialState)==null||(r=o.pagination)==null?void 0:r.pageIndex)!=null?s:ms)},resetPageSize:i=>{var s,o,r;e.setPageSize(i?_s:(s=(o=e.initialState)==null||(r=o.pagination)==null?void 0:r.pageSize)!=null?s:_s)},setPageSize:i=>{e.setPagination(s=>{const o=Math.max(1,he(i,s.pageSize)),r=s.pageSize*s.pageIndex,a=Math.floor(r/o);return{...s,pageIndex:a,pageSize:o}})},setPageCount:i=>e.setPagination(s=>{var o;let r=he(i,(o=e.options.pageCount)!=null?o:-1);return typeof r=="number"&&(r=Math.max(-1,r)),{...s,pageCount:r}}),getPageOptions:I(()=>[e.getPageCount()],i=>{let s=[];return i&&i>0&&(s=[...new Array(i)].fill(null).map((o,r)=>r)),s},{key:!1,debug:()=>{var i;return(i=e.options.debugAll)!=null?i:e.options.debugTable}}),getCanPreviousPage:()=>e.getState().pagination.pageIndex>0,getCanNextPage:()=>{const{pageIndex:i}=e.getState().pagination,s=e.getPageCount();return s===-1?!0:s===0?!1:i<s-1},previousPage:()=>e.setPageIndex(i=>i-1),nextPage:()=>e.setPageIndex(i=>i+1),getPrePaginationRowModel:()=>e.getExpandedRowModel(),getPaginationRowModel:()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),getPageCount:()=>{var i;return(i=e.options.pageCount)!=null?i:Math.ceil(e.getPrePaginationRowModel().rows.length/e.getState().pagination.pageSize)}}}},Ui=()=>({left:[],right:[]}),Au={getInitialState:e=>({columnPinning:Ui(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Vt("columnPinning",e)}),createColumn:(e,t)=>({pin:n=>{const i=e.getLeafColumns().map(s=>s.id).filter(Boolean);t.setColumnPinning(s=>{var o,r;if(n==="right"){var a,l;return{left:((a=s==null?void 0:s.left)!=null?a:[]).filter(f=>!(i!=null&&i.includes(f))),right:[...((l=s==null?void 0:s.right)!=null?l:[]).filter(f=>!(i!=null&&i.includes(f))),...i]}}if(n==="left"){var c,u;return{left:[...((c=s==null?void 0:s.left)!=null?c:[]).filter(f=>!(i!=null&&i.includes(f))),...i],right:((u=s==null?void 0:s.right)!=null?u:[]).filter(f=>!(i!=null&&i.includes(f)))}}return{left:((o=s==null?void 0:s.left)!=null?o:[]).filter(f=>!(i!=null&&i.includes(f))),right:((r=s==null?void 0:s.right)!=null?r:[]).filter(f=>!(i!=null&&i.includes(f)))}})},getCanPin:()=>e.getLeafColumns().some(i=>{var s,o;return((s=i.columnDef.enablePinning)!=null?s:!0)&&((o=t.options.enablePinning)!=null?o:!0)}),getIsPinned:()=>{const n=e.getLeafColumns().map(a=>a.id),{left:i,right:s}=t.getState().columnPinning,o=n.some(a=>i==null?void 0:i.includes(a)),r=n.some(a=>s==null?void 0:s.includes(a));return o?"left":r?"right":!1},getPinnedIndex:()=>{var n,i,s;const o=e.getIsPinned();return o?(n=(i=t.getState().columnPinning)==null||(s=i[o])==null?void 0:s.indexOf(e.id))!=null?n:-1:0}}),createRow:(e,t)=>({getCenterVisibleCells:I(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,i,s)=>{const o=[...i??[],...s??[]];return n.filter(r=>!o.includes(r.column.id))},{key:"row.getCenterVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),getLeftVisibleCells:I(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,,],(n,i)=>(i??[]).map(o=>n.find(r=>r.column.id===o)).filter(Boolean).map(o=>({...o,position:"left"})),{key:"row.getLeftVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),getRightVisibleCells:I(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,i)=>(i??[]).map(o=>n.find(r=>r.column.id===o)).filter(Boolean).map(o=>({...o,position:"right"})),{key:"row.getRightVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})}),createTable:e=>({setColumnPinning:t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),resetColumnPinning:t=>{var n,i;return e.setColumnPinning(t?Ui():(n=(i=e.initialState)==null?void 0:i.columnPinning)!=null?n:Ui())},getIsSomeColumnsPinned:t=>{var n;const i=e.getState().columnPinning;if(!t){var s,o;return!!((s=i.left)!=null&&s.length||(o=i.right)!=null&&o.length)}return!!((n=i[t])!=null&&n.length)},getLeftLeafColumns:I(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(i=>t.find(s=>s.id===i)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),getRightLeafColumns:I(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(i=>t.find(s=>s.id===i)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),getCenterLeafColumns:I(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,i)=>{const s=[...n??[],...i??[]];return t.filter(o=>!s.includes(o.id))},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}})})},Pu={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Vt("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>({setRowSelection:t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),resetRowSelection:t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},toggleAllRowsSelected:t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const i={...n},s=e.getPreGroupedRowModel().flatRows;return t?s.forEach(o=>{o.getCanSelect()&&(i[o.id]=!0)}):s.forEach(o=>{delete i[o.id]}),i})},toggleAllPageRowsSelected:t=>e.setRowSelection(n=>{const i=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),s={...n};return e.getRowModel().rows.forEach(o=>{bs(s,o.id,i,e)}),s}),getPreSelectedRowModel:()=>e.getCoreRowModel(),getSelectedRowModel:I(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?Xi(e,n):{rows:[],flatRows:[],rowsById:{}},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),getFilteredSelectedRowModel:I(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?Xi(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getFilteredSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),getGroupedSelectedRowModel:I(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?Xi(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getGroupedSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),getIsAllRowsSelected:()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let i=!!(t.length&&Object.keys(n).length);return i&&t.some(s=>s.getCanSelect()&&!n[s.id])&&(i=!1),i},getIsAllPageRowsSelected:()=>{const t=e.getPaginationRowModel().flatRows,{rowSelection:n}=e.getState();let i=!!t.length;return i&&t.some(s=>s.getCanSelect()&&!n[s.id])&&(i=!1),i},getIsSomeRowsSelected:()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},getIsSomePageRowsSelected:()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.some(n=>n.getIsSelected()||n.getIsSomeSelected())},getToggleAllRowsSelectedHandler:()=>t=>{e.toggleAllRowsSelected(t.target.checked)},getToggleAllPageRowsSelectedHandler:()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}}),createRow:(e,t)=>({toggleSelected:n=>{const i=e.getIsSelected();t.setRowSelection(s=>{if(n=typeof n<"u"?n:!i,i===n)return s;const o={...s};return bs(o,e.id,n,t),o})},getIsSelected:()=>{const{rowSelection:n}=t.getState();return Ns(e,n)},getIsSomeSelected:()=>{const{rowSelection:n}=t.getState();return _o(e,n)==="some"},getIsAllSubRowsSelected:()=>{const{rowSelection:n}=t.getState();return _o(e,n)==="all"},getCanSelect:()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},getCanSelectSubRows:()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},getCanMultiSelect:()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},getToggleSelectedHandler:()=>{const n=e.getCanSelect();return i=>{var s;n&&e.toggleSelected((s=i.target)==null?void 0:s.checked)}}})},bs=(e,t,n,i)=>{var s;const o=i.getRow(t);n?(o.getCanMultiSelect()||Object.keys(e).forEach(r=>delete e[r]),o.getCanSelect()&&(e[t]=!0)):delete e[t],(s=o.subRows)!=null&&s.length&&o.getCanSelectSubRows()&&o.subRows.forEach(r=>bs(e,r.id,n,i))};function Xi(e,t){const n=e.getState().rowSelection,i=[],s={},o=function(r,a){return r.map(l=>{var c;const u=Ns(l,n);if(u&&(i.push(l),s[l.id]=l),(c=l.subRows)!=null&&c.length&&(l={...l,subRows:o(l.subRows)}),u)return l}).filter(Boolean)};return{rows:o(t.rows),flatRows:i,rowsById:s}}function Ns(e,t){var n;return(n=t[e.id])!=null?n:!1}function _o(e,t,n){if(e.subRows&&e.subRows.length){let i=!0,s=!1;return e.subRows.forEach(o=>{s&&!i||(Ns(o,t)?s=!0:i=!1)}),i?"all":s?"some":!1}return!1}const ys=/([0-9]+)/gm,Fu=(e,t,n)=>$a(ve(e.getValue(n)).toLowerCase(),ve(t.getValue(n)).toLowerCase()),Du=(e,t,n)=>$a(ve(e.getValue(n)),ve(t.getValue(n))),Ou=(e,t,n)=>Bs(ve(e.getValue(n)).toLowerCase(),ve(t.getValue(n)).toLowerCase()),Eu=(e,t,n)=>Bs(ve(e.getValue(n)),ve(t.getValue(n))),Lu=(e,t,n)=>{const i=e.getValue(n),s=t.getValue(n);return i>s?1:i<s?-1:0},Tu=(e,t,n)=>Bs(e.getValue(n),t.getValue(n));function Bs(e,t){return e===t?0:e>t?1:-1}function ve(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function $a(e,t){const n=e.split(ys).filter(Boolean),i=t.split(ys).filter(Boolean);for(;n.length&&i.length;){const s=n.shift(),o=i.shift(),r=parseInt(s,10),a=parseInt(o,10),l=[r,a].sort();if(isNaN(l[0])){if(s>o)return 1;if(o>s)return-1;continue}if(isNaN(l[1]))return isNaN(r)?-1:1;if(r>a)return 1;if(a>r)return-1}return n.length-i.length}const sn={alphanumeric:Fu,alphanumericCaseSensitive:Du,text:Ou,textCaseSensitive:Eu,datetime:Lu,basic:Tu},Iu={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto"}),getDefaultOptions:e=>({onSortingChange:Vt("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>({getAutoSortingFn:()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let i=!1;for(const s of n){const o=s==null?void 0:s.getValue(e.id);if(Object.prototype.toString.call(o)==="[object Date]")return sn.datetime;if(typeof o=="string"&&(i=!0,o.split(ys).length>1))return sn.alphanumeric}return i?sn.text:sn.basic},getAutoSortDir:()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},getSortingFn:()=>{var n,i;if(!e)throw new Error;return ui(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(i=t.options.sortingFns)==null?void 0:i[e.columnDef.sortingFn])!=null?n:sn[e.columnDef.sortingFn]},toggleSorting:(n,i)=>{const s=e.getNextSortingOrder(),o=typeof n<"u"&&n!==null;t.setSorting(r=>{const a=r==null?void 0:r.find(h=>h.id===e.id),l=r==null?void 0:r.findIndex(h=>h.id===e.id);let c=[],u,f=o?n:s==="desc";if(r!=null&&r.length&&e.getCanMultiSort()&&i?a?u="toggle":u="add":r!=null&&r.length&&l!==r.length-1?u="replace":a?u="toggle":u="replace",u==="toggle"&&(o||s||(u="remove")),u==="add"){var d;c=[...r,{id:e.id,desc:f}],c.splice(0,c.length-((d=t.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else u==="toggle"?c=r.map(h=>h.id===e.id?{...h,desc:f}:h):u==="remove"?c=r.filter(h=>h.id!==e.id):c=[{id:e.id,desc:f}];return c})},getFirstSortDir:()=>{var n,i;return((n=(i=e.columnDef.sortDescFirst)!=null?i:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},getNextSortingOrder:n=>{var i,s;const o=e.getFirstSortDir(),r=e.getIsSorted();return r?r!==o&&((i=t.options.enableSortingRemoval)==null||i)&&(!(n&&(s=t.options.enableMultiRemove)!=null)||s)?!1:r==="desc"?"asc":"desc":o},getCanSort:()=>{var n,i;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((i=t.options.enableSorting)!=null?i:!0)&&!!e.accessorFn},getCanMultiSort:()=>{var n,i;return(n=(i=e.columnDef.enableMultiSort)!=null?i:t.options.enableMultiSort)!=null?n:!!e.accessorFn},getIsSorted:()=>{var n;const i=(n=t.getState().sorting)==null?void 0:n.find(s=>s.id===e.id);return i?i.desc?"desc":"asc":!1},getSortIndex:()=>{var n,i;return(n=(i=t.getState().sorting)==null?void 0:i.findIndex(s=>s.id===e.id))!=null?n:-1},clearSorting:()=>{t.setSorting(n=>n!=null&&n.length?n.filter(i=>i.id!==e.id):[])},getToggleSortingHandler:()=>{const n=e.getCanSort();return i=>{n&&(i.persist==null||i.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(i):!1))}}}),createTable:e=>({setSorting:t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),resetSorting:t=>{var n,i;e.setSorting(t?[]:(n=(i=e.initialState)==null?void 0:i.sorting)!=null?n:[])},getPreSortedRowModel:()=>e.getGroupedRowModel(),getSortedRowModel:()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())})},Vu={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Vt("columnVisibility",e)}),createColumn:(e,t)=>({toggleVisibility:n=>{e.getCanHide()&&t.setColumnVisibility(i=>({...i,[e.id]:n??!e.getIsVisible()}))},getIsVisible:()=>{var n,i;return(n=(i=t.getState().columnVisibility)==null?void 0:i[e.id])!=null?n:!0},getCanHide:()=>{var n,i;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((i=t.options.enableHiding)!=null?i:!0)},getToggleVisibilityHandler:()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}}),createRow:(e,t)=>({_getAllVisibleCells:I(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(i=>i.column.getIsVisible()),{key:"row._getAllVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),getVisibleCells:I(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,i,s)=>[...n,...i,...s],{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})}),createTable:e=>{const t=(n,i)=>I(()=>[i(),i().filter(s=>s.getIsVisible()).map(s=>s.id).join("_")],s=>s.filter(o=>o.getIsVisible==null?void 0:o.getIsVisible()),{key:n,debug:()=>{var s;return(s=e.options.debugAll)!=null?s:e.options.debugColumns}});return{getVisibleFlatColumns:t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),getVisibleLeafColumns:t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),getLeftVisibleLeafColumns:t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),getRightVisibleLeafColumns:t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),getCenterVisibleLeafColumns:t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),setColumnVisibility:n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),resetColumnVisibility:n=>{var i;e.setColumnVisibility(n?{}:(i=e.initialState.columnVisibility)!=null?i:{})},toggleAllColumnsVisible:n=>{var i;n=(i=n)!=null?i:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((s,o)=>({...s,[o.id]:n||!(o.getCanHide!=null&&o.getCanHide())}),{}))},getIsAllColumnsVisible:()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),getIsSomeColumnsVisible:()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),getToggleAllColumnsVisibilityHandler:()=>n=>{var i;e.toggleAllColumnsVisible((i=n.target)==null?void 0:i.checked)}}}},bo=[fu,Vu,Ru,Au,pu,Iu,Mu,gu,$u,Pu,du];function zu(e){var t;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");let n={_features:bo};const i=n._features.reduce((u,f)=>Object.assign(u,f.getDefaultOptions==null?void 0:f.getDefaultOptions(n)),{}),s=u=>n.options.mergeOptions?n.options.mergeOptions(i,u):{...i,...u};let r={...{},...(t=e.initialState)!=null?t:{}};n._features.forEach(u=>{var f;r=(f=u.getInitialState==null?void 0:u.getInitialState(r))!=null?f:r});const a=[];let l=!1;const c={_features:bo,options:{...i,...e},initialState:r,_queue:u=>{a.push(u),l||(l=!0,Promise.resolve().then(()=>{for(;a.length;)a.shift()();l=!1}).catch(f=>setTimeout(()=>{throw f})))},reset:()=>{n.setState(n.initialState)},setOptions:u=>{const f=he(u,n.options);n.options=s(f)},getState:()=>n.options.state,setState:u=>{n.options.onStateChange==null||n.options.onStateChange(u)},_getRowId:(u,f,d)=>{var h;return(h=n.options.getRowId==null?void 0:n.options.getRowId(u,f,d))!=null?h:`${d?[d.id,f].join("."):f}`},getCoreRowModel:()=>(n._getCoreRowModel||(n._getCoreRowModel=n.options.getCoreRowModel(n)),n._getCoreRowModel()),getRowModel:()=>n.getPaginationRowModel(),getRow:u=>{const f=n.getRowModel().rowsById[u];if(!f)throw new Error;return f},_getDefaultColumnDef:I(()=>[n.options.defaultColumn],u=>{var f;return u=(f=u)!=null?f:{},{header:d=>{const h=d.header.column.columnDef;return h.accessorKey?h.accessorKey:h.accessorFn?h.id:null},cell:d=>{var h,g;return(h=(g=d.renderValue())==null||g.toString==null?void 0:g.toString())!=null?h:null},...n._features.reduce((d,h)=>Object.assign(d,h.getDefaultColumnDef==null?void 0:h.getDefaultColumnDef()),{}),...u}},{debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns},key:!1}),_getColumnDefs:()=>n.options.columns,getAllColumns:I(()=>[n._getColumnDefs()],u=>{const f=function(d,h,g){return g===void 0&&(g=0),d.map(p=>{const m=uu(n,p,g,h),_=p;return m.columns=_.columns?f(_.columns,m,g+1):[],m})};return f(u)},{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),getAllFlatColumns:I(()=>[n.getAllColumns()],u=>u.flatMap(f=>f.getFlatColumns()),{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),_getAllFlatColumnsById:I(()=>[n.getAllFlatColumns()],u=>u.reduce((f,d)=>(f[d.id]=d,f),{}),{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),getAllLeafColumns:I(()=>[n.getAllColumns(),n._getOrderColumnsFn()],(u,f)=>{let d=u.flatMap(h=>h.getLeafColumns());return f(d)},{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),getColumn:u=>n._getAllFlatColumnsById()[u]};return Object.assign(n,c),n._features.forEach(u=>Object.assign(n,u.createTable==null?void 0:u.createTable(n))),n}function Hu(e,t,n,i){const s=()=>{var r;return(r=o.getValue())!=null?r:e.options.renderFallbackValue},o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(i),renderValue:s,getContext:I(()=>[e,n,t,o],(r,a,l,c)=>({table:r,column:a,row:l,cell:c,getValue:c.getValue,renderValue:c.renderValue}),{key:!1,debug:()=>e.options.debugAll})};return e._features.forEach(r=>{Object.assign(o,r.createCell==null?void 0:r.createCell(o,n,t,e))},{}),o}const Nu=(e,t,n,i,s,o,r)=>{let a={id:t,index:i,original:n,depth:s,parentRow:r,_valuesCache:{},_uniqueValuesCache:{},getValue:l=>{if(a._valuesCache.hasOwnProperty(l))return a._valuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return a._valuesCache[l]=c.accessorFn(a.original,i),a._valuesCache[l]},getUniqueValues:l=>{if(a._uniqueValuesCache.hasOwnProperty(l))return a._uniqueValuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(a._uniqueValuesCache[l]=c.columnDef.getUniqueValues(a.original,i),a._uniqueValuesCache[l]):(a._uniqueValuesCache[l]=[a.getValue(l)],a._uniqueValuesCache[l])},renderValue:l=>{var c;return(c=a.getValue(l))!=null?c:e.options.renderFallbackValue},subRows:o??[],getLeafRows:()=>cu(a.subRows,l=>l.subRows),getAllCells:I(()=>[e.getAllLeafColumns()],l=>l.map(c=>Hu(e,a,c,c.id)),{key:!1,debug:()=>{var l;return(l=e.options.debugAll)!=null?l:e.options.debugRows}}),_getAllCellsByColumnId:I(()=>[a.getAllCells()],l=>l.reduce((c,u)=>(c[u.column.id]=u,c),{}),{key:"row.getAllCellsByColumnId",debug:()=>{var l;return(l=e.options.debugAll)!=null?l:e.options.debugRows}})};for(let l=0;l<e._features.length;l++){const c=e._features[l];Object.assign(a,c==null||c.createRow==null?void 0:c.createRow(a,e))}return a};function Bu(){return e=>I(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},i=function(s,o,r){o===void 0&&(o=0);const a=[];for(let c=0;c<s.length;c++){const u=Nu(e,e._getRowId(s[c],c,r),s[c],c,o,void 0,r);if(n.flatRows.push(u),n.rowsById[u.id]=u,a.push(u),e.options.getSubRows){var l;u.originalSubRows=e.options.getSubRows(s[c],c),(l=u.originalSubRows)!=null&&l.length&&(u.subRows=i(u.originalSubRows,o+1,u))}}return a};return n.rows=i(t),n},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}/**
 * svelte-table
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ju(e){let t;return{c(){t=J(e[0])},l(n){t=ql(n,e[0])},m(n,i){Nl(n,t,i)},p(n,[i]){i&1&&Wt(t,n[0])},i:ht,o:ht,d(n){n&&U(t)}}}function Wu(e,t,n){let{content:i}=t;return e.$$set=s=>{"content"in s&&n(0,i=s.content)},[i]}class Gu extends X{constructor(t){super(),Q(this,t,Wu,ju,Y,{content:0})}}const qu=fa((e,t,n,i)=>`${lc(t.content)}`);var Yu=typeof document>"u"?qu:Gu;function Uu(e,t,n){let i,s;return i=new t({props:n,$$inline:!0}),{c(){V(i.$$.fragment)},l(o){uc(i.$$.fragment,o)},m(o,r){E(i,o,r),s=!0},p:ht,i(o){s||(S(i.$$.fragment,o),s=!0)},o(o){C(i.$$.fragment,o),s=!1},d(o){L(i,o)}}}function Xu(e,t){return class extends X{constructor(i){super(),Q(this,i,null,s=>Uu(s,e,t),Y,{},void 0)}}}function Ku(e,t){return fa((i,s,o,r)=>`${cc(e,"TableComponent").$$render(i,t,{},{})}`)}const Aa=typeof window>"u"?Ku:Xu;function Qu(e){return typeof e=="object"&&typeof e.$$render=="function"&&typeof e.render=="function"}function Zu(e){var t,n;let i="__SVELTE_HMR"in window;return e.prototype instanceof X||i&&((t=e.name)==null?void 0:t.startsWith("Proxy<"))&&((n=e.name)==null?void 0:n.endsWith(">"))}function yo(e){return typeof document>"u"?Qu(e):Zu(e)}function vo(e){return Aa(Yu,{content:e})}function fi(e,t){if(!e)return null;if(yo(e))return Aa(e,t);if(typeof e=="function"){const n=e(t);return yo(n)?n:vo(n)}return vo(e)}function Ju(e){let t;"subscribe"in e?t=e:t=cs(e);let n={state:{},onStateChange:()=>{},renderFallbackValue:null,...Fl(t)},i=zu(n),s=Ot(i.initialState),o=Is([s,t],a=>a);return cs(i,function(l){const c=o.subscribe(u=>{let[f,d]=u;i.setOptions(h=>({...h,...d,state:{...f,...d.state},onStateChange:g=>{g instanceof Function?s.update(g):s.set(g),n.onStateChange==null||n.onStateChange(g)}})),l(i)});return function(){c()}})}function Pi(e){return e.split("-")[1]}function Pa(e){return e==="y"?"height":"width"}function Fe(e){return e.split("-")[0]}function Fi(e){return["top","bottom"].includes(Fe(e))?"x":"y"}function xo(e,t,n){let{reference:i,floating:s}=e;const o=i.x+i.width/2-s.width/2,r=i.y+i.height/2-s.height/2,a=Fi(t),l=Pa(a),c=i[l]/2-s[l]/2,u=a==="x";let f;switch(Fe(t)){case"top":f={x:o,y:i.y-s.height};break;case"bottom":f={x:o,y:i.y+i.height};break;case"right":f={x:i.x+i.width,y:r};break;case"left":f={x:i.x-s.width,y:r};break;default:f={x:i.x,y:i.y}}switch(Pi(t)){case"start":f[a]-=c*(n&&u?-1:1);break;case"end":f[a]+=c*(n&&u?-1:1)}return f}const tf=async(e,t,n)=>{const{placement:i="bottom",strategy:s="absolute",middleware:o=[],platform:r}=n,a=o.filter(Boolean),l=await(r.isRTL==null?void 0:r.isRTL(t));let c=await r.getElementRects({reference:e,floating:t,strategy:s}),{x:u,y:f}=xo(c,i,l),d=i,h={},g=0;for(let p=0;p<a.length;p++){const{name:m,fn:_}=a[p],{x:b,y,data:x,reset:v}=await _({x:u,y:f,initialPlacement:i,placement:d,strategy:s,middlewareData:h,rects:c,platform:r,elements:{reference:e,floating:t}});u=b??u,f=y??f,h={...h,[m]:{...h[m],...x}},v&&g<=50&&(g++,typeof v=="object"&&(v.placement&&(d=v.placement),v.rects&&(c=v.rects===!0?await r.getElementRects({reference:e,floating:t,strategy:s}):v.rects),{x:u,y:f}=xo(c,d,l)),p=-1)}return{x:u,y:f,placement:d,strategy:s,middlewareData:h}};function ef(e){return typeof e!="number"?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(e):{top:e,right:e,bottom:e,left:e}}function di(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}async function Fa(e,t){var n;t===void 0&&(t={});const{x:i,y:s,platform:o,rects:r,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:d=!1,padding:h=0}=t,g=ef(h),p=a[d?f==="floating"?"reference":"floating":f],m=di(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(p)))==null||n?p:p.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),_=f==="floating"?{...r.floating,x:i,y:s}:r.reference,b=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),y=await(o.isElement==null?void 0:o.isElement(b))&&await(o.getScale==null?void 0:o.getScale(b))||{x:1,y:1},x=di(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({rect:_,offsetParent:b,strategy:l}):_);return{top:(m.top-x.top+g.top)/y.y,bottom:(x.bottom-m.bottom+g.bottom)/y.y,left:(m.left-x.left+g.left)/y.x,right:(x.right-m.right+g.right)/y.x}}const nf=Math.min,sf=Math.max;function wo(e,t,n){return sf(e,nf(t,n))}const of=["top","right","bottom","left"];of.reduce((e,t)=>e.concat(t,t+"-start",t+"-end"),[]);const rf={left:"right",right:"left",bottom:"top",top:"bottom"};function hi(e){return e.replace(/left|right|bottom|top/g,t=>rf[t])}function af(e,t,n){n===void 0&&(n=!1);const i=Pi(e),s=Fi(e),o=Pa(s);let r=s==="x"?i===(n?"end":"start")?"right":"left":i==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(r=hi(r)),{main:r,cross:hi(r)}}const lf={start:"end",end:"start"};function Ki(e){return e.replace(/start|end/g,t=>lf[t])}const cf=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n;const{placement:i,middlewareData:s,rects:o,initialPlacement:r,platform:a,elements:l}=t,{mainAxis:c=!0,crossAxis:u=!0,fallbackPlacements:f,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:g=!0,...p}=e,m=Fe(i),_=Fe(r)===r,b=await(a.isRTL==null?void 0:a.isRTL(l.floating)),y=f||(_||!g?[hi(r)]:function(M){const O=hi(M);return[Ki(M),O,Ki(O)]}(r));f||h==="none"||y.push(...function(M,O,K,Z){const T=Pi(M);let z=function(B,ft,st){const wt=["left","right"],Xt=["right","left"],pt=["top","bottom"],mt=["bottom","top"];switch(B){case"top":case"bottom":return st?ft?Xt:wt:ft?wt:Xt;case"left":case"right":return ft?pt:mt;default:return[]}}(Fe(M),K==="start",Z);return T&&(z=z.map(B=>B+"-"+T),O&&(z=z.concat(z.map(Ki)))),z}(r,g,h,b));const x=[r,...y],v=await Fa(t,p),R=[];let k=((n=s.flip)==null?void 0:n.overflows)||[];if(c&&R.push(v[m]),u){const{main:M,cross:O}=af(i,o,b);R.push(v[M],v[O])}if(k=[...k,{placement:i,overflows:R}],!R.every(M=>M<=0)){var $,P;const M=((($=s.flip)==null?void 0:$.index)||0)+1,O=x[M];if(O)return{data:{index:M,overflows:k},reset:{placement:O}};let K=(P=k.filter(Z=>Z.overflows[0]<=0).sort((Z,T)=>Z.overflows[1]-T.overflows[1])[0])==null?void 0:P.placement;if(!K)switch(d){case"bestFit":{var F;const Z=(F=k.map(T=>[T.placement,T.overflows.filter(z=>z>0).reduce((z,B)=>z+B,0)]).sort((T,z)=>T[1]-z[1])[0])==null?void 0:F[0];Z&&(K=Z);break}case"initialPlacement":K=r}if(i!==K)return{reset:{placement:K}}}return{}}}},uf=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){const{x:n,y:i}=t,s=await async function(o,r){const{placement:a,platform:l,elements:c}=o,u=await(l.isRTL==null?void 0:l.isRTL(c.floating)),f=Fe(a),d=Pi(a),h=Fi(a)==="x",g=["left","top"].includes(f)?-1:1,p=u&&h?-1:1,m=typeof r=="function"?r(o):r;let{mainAxis:_,crossAxis:b,alignmentAxis:y}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...m};return d&&typeof y=="number"&&(b=d==="end"?-1*y:y),h?{x:b*p,y:_*g}:{x:_*g,y:b*p}}(t,e);return{x:n+s.x,y:i+s.y,data:s}}}};function ff(e){return e==="x"?"y":"x"}const df=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:i,placement:s}=t,{mainAxis:o=!0,crossAxis:r=!1,limiter:a={fn:m=>{let{x:_,y:b}=m;return{x:_,y:b}}},...l}=e,c={x:n,y:i},u=await Fa(t,l),f=Fi(Fe(s)),d=ff(f);let h=c[f],g=c[d];if(o){const m=f==="y"?"bottom":"right";h=wo(h+u[f==="y"?"top":"left"],h,h-u[m])}if(r){const m=d==="y"?"bottom":"right";g=wo(g+u[d==="y"?"top":"left"],g,g-u[m])}const p=a.fn({...t,[f]:h,[d]:g});return{...p,data:{x:p.x-n,y:p.y-i}}}}};function Ct(e){var t;return((t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Jt(e){return Ct(e).getComputedStyle(e)}function Da(e){return e instanceof Ct(e).Node}function xe(e){return Da(e)?(e.nodeName||"").toLowerCase():""}let Ln;function Oa(){if(Ln)return Ln;const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?(Ln=e.brands.map(t=>t.brand+"/"+t.version).join(" "),Ln):navigator.userAgent}function Yt(e){return e instanceof Ct(e).HTMLElement}function qt(e){return e instanceof Ct(e).Element}function So(e){return typeof ShadowRoot>"u"?!1:e instanceof Ct(e).ShadowRoot||e instanceof ShadowRoot}function Di(e){const{overflow:t,overflowX:n,overflowY:i,display:s}=Jt(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(s)}function hf(e){return["table","td","th"].includes(xe(e))}function vs(e){const t=/firefox/i.test(Oa()),n=Jt(e),i=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||!!i&&i!=="none"||t&&n.willChange==="filter"||t&&!!n.filter&&n.filter!=="none"||["transform","perspective"].some(s=>n.willChange.includes(s))||["paint","layout","strict","content"].some(s=>{const o=n.contain;return o!=null&&o.includes(s)})}function xs(){return/^((?!chrome|android).)*safari/i.test(Oa())}function js(e){return["html","body","#document"].includes(xe(e))}const Co=Math.min,hn=Math.max,gi=Math.round;function Ea(e){const t=Jt(e);let n=parseFloat(t.width),i=parseFloat(t.height);const s=Yt(e),o=s?e.offsetWidth:n,r=s?e.offsetHeight:i,a=gi(n)!==o||gi(i)!==r;return a&&(n=o,i=r),{width:n,height:i,fallback:a}}function La(e){return qt(e)?e:e.contextElement}const Ta={x:1,y:1};function Ye(e){const t=La(e);if(!Yt(t))return Ta;const n=t.getBoundingClientRect(),{width:i,height:s,fallback:o}=Ea(t);let r=(o?gi(n.width):n.width)/i,a=(o?gi(n.height):n.height)/s;return r&&Number.isFinite(r)||(r=1),a&&Number.isFinite(a)||(a=1),{x:r,y:a}}function Le(e,t,n,i){var s,o;t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),a=La(e);let l=Ta;t&&(i?qt(i)&&(l=Ye(i)):l=Ye(e));const c=a?Ct(a):window,u=xs()&&n;let f=(r.left+(u&&((s=c.visualViewport)==null?void 0:s.offsetLeft)||0))/l.x,d=(r.top+(u&&((o=c.visualViewport)==null?void 0:o.offsetTop)||0))/l.y,h=r.width/l.x,g=r.height/l.y;if(a){const p=Ct(a),m=i&&qt(i)?Ct(i):i;let _=p.frameElement;for(;_&&i&&m!==p;){const b=Ye(_),y=_.getBoundingClientRect(),x=getComputedStyle(_);y.x+=(_.clientLeft+parseFloat(x.paddingLeft))*b.x,y.y+=(_.clientTop+parseFloat(x.paddingTop))*b.y,f*=b.x,d*=b.y,h*=b.x,g*=b.y,f+=y.x,d+=y.y,_=Ct(_).frameElement}}return di({width:h,height:g,x:f,y:d})}function _e(e){return((Da(e)?e.ownerDocument:e.document)||window.document).documentElement}function Oi(e){return qt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Ia(e){return Le(_e(e)).left+Oi(e).scrollLeft}function vn(e){if(xe(e)==="html")return e;const t=e.assignedSlot||e.parentNode||So(e)&&e.host||_e(e);return So(t)?t.host:t}function Va(e){const t=vn(e);return js(t)?t.ownerDocument.body:Yt(t)&&Di(t)?t:Va(t)}function gn(e,t){var n;t===void 0&&(t=[]);const i=Va(e),s=i===((n=e.ownerDocument)==null?void 0:n.body),o=Ct(i);return s?t.concat(o,o.visualViewport||[],Di(i)?i:[]):t.concat(i,gn(i))}function Mo(e,t,n){let i;if(t==="viewport")i=function(r,a){const l=Ct(r),c=_e(r),u=l.visualViewport;let f=c.clientWidth,d=c.clientHeight,h=0,g=0;if(u){f=u.width,d=u.height;const p=xs();(!p||p&&a==="fixed")&&(h=u.offsetLeft,g=u.offsetTop)}return{width:f,height:d,x:h,y:g}}(e,n);else if(t==="document")i=function(r){const a=_e(r),l=Oi(r),c=r.ownerDocument.body,u=hn(a.scrollWidth,a.clientWidth,c.scrollWidth,c.clientWidth),f=hn(a.scrollHeight,a.clientHeight,c.scrollHeight,c.clientHeight);let d=-l.scrollLeft+Ia(r);const h=-l.scrollTop;return Jt(c).direction==="rtl"&&(d+=hn(a.clientWidth,c.clientWidth)-u),{width:u,height:f,x:d,y:h}}(_e(e));else if(qt(t))i=function(r,a){const l=Le(r,!0,a==="fixed"),c=l.top+r.clientTop,u=l.left+r.clientLeft,f=Yt(r)?Ye(r):{x:1,y:1};return{width:r.clientWidth*f.x,height:r.clientHeight*f.y,x:u*f.x,y:c*f.y}}(t,n);else{const r={...t};if(xs()){var s,o;const a=Ct(e);r.x-=((s=a.visualViewport)==null?void 0:s.offsetLeft)||0,r.y-=((o=a.visualViewport)==null?void 0:o.offsetTop)||0}i=r}return di(i)}function ko(e,t){return Yt(e)&&Jt(e).position!=="fixed"?t?t(e):e.offsetParent:null}function Ro(e,t){const n=Ct(e);if(!Yt(e))return n;let i=ko(e,t);for(;i&&hf(i)&&Jt(i).position==="static";)i=ko(i,t);return i&&(xe(i)==="html"||xe(i)==="body"&&Jt(i).position==="static"&&!vs(i))?n:i||function(s){let o=vn(s);for(;Yt(o)&&!js(o);){if(vs(o))return o;o=vn(o)}return null}(e)||n}function gf(e,t,n){const i=Yt(t),s=_e(t),o=Le(e,!0,n==="fixed",t);let r={scrollLeft:0,scrollTop:0};const a={x:0,y:0};if(i||!i&&n!=="fixed")if((xe(t)!=="body"||Di(s))&&(r=Oi(t)),Yt(t)){const l=Le(t,!0);a.x=l.x+t.clientLeft,a.y=l.y+t.clientTop}else s&&(a.x=Ia(s));return{x:o.left+r.scrollLeft-a.x,y:o.top+r.scrollTop-a.y,width:o.width,height:o.height}}const pf={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:s}=e;const o=n==="clippingAncestors"?function(c,u){const f=u.get(c);if(f)return f;let d=gn(c).filter(m=>qt(m)&&xe(m)!=="body"),h=null;const g=Jt(c).position==="fixed";let p=g?vn(c):c;for(;qt(p)&&!js(p);){const m=Jt(p),_=vs(p);m.position==="fixed"?h=null:(g?_||h:_||m.position!=="static"||!h||!["absolute","fixed"].includes(h.position))?h=m:d=d.filter(b=>b!==p),p=vn(p)}return u.set(c,d),d}(t,this._c):[].concat(n),r=[...o,i],a=r[0],l=r.reduce((c,u)=>{const f=Mo(t,u,s);return c.top=hn(f.top,c.top),c.right=Co(f.right,c.right),c.bottom=Co(f.bottom,c.bottom),c.left=hn(f.left,c.left),c},Mo(t,a,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:i}=e;const s=Yt(n),o=_e(n);if(n===o)return t;let r={scrollLeft:0,scrollTop:0},a={x:1,y:1};const l={x:0,y:0};if((s||!s&&i!=="fixed")&&((xe(n)!=="body"||Di(o))&&(r=Oi(n)),Yt(n))){const c=Le(n);a=Ye(n),l.x=c.x+n.clientLeft,l.y=c.y+n.clientTop}return{width:t.width*a.x,height:t.height*a.y,x:t.x*a.x-r.scrollLeft*a.x+l.x,y:t.y*a.y-r.scrollTop*a.y+l.y}},isElement:qt,getDimensions:function(e){return Ea(e)},getOffsetParent:Ro,getDocumentElement:_e,getScale:Ye,async getElementRects(e){let{reference:t,floating:n,strategy:i}=e;const s=this.getOffsetParent||Ro,o=this.getDimensions;return{reference:gf(t,await s(n),i),floating:{x:0,y:0,...await o(n)}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>Jt(e).direction==="rtl"};function mf(e,t,n,i){i===void 0&&(i={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:r=!0,animationFrame:a=!1}=i,l=s&&!a,c=l||o?[...qt(e)?gn(e):e.contextElement?gn(e.contextElement):[],...gn(t)]:[];c.forEach(h=>{l&&h.addEventListener("scroll",n,{passive:!0}),o&&h.addEventListener("resize",n)});let u,f=null;r&&(f=new ResizeObserver(()=>{n()}),qt(e)&&!a&&f.observe(e),qt(e)||!e.contextElement||a||f.observe(e.contextElement),f.observe(t));let d=a?Le(e):null;return a&&function h(){const g=Le(e);!d||g.x===d.x&&g.y===d.y&&g.width===d.width&&g.height===d.height||n(),d=g,u=requestAnimationFrame(h)}(),n(),()=>{var h;c.forEach(g=>{l&&g.removeEventListener("scroll",n),o&&g.removeEventListener("resize",n)}),(h=f)==null||h.disconnect(),f=null,a&&cancelAnimationFrame(u)}}const _f=(e,t,n)=>{const i=new Map,s={platform:pf,...n},o={...s.platform,_c:i};return tf(e,t,{...s,platform:o})};function bf(e){let t,n;const i={autoUpdate:!0};let s=e;const o=u=>({...i,...e||{},...u||{}}),r=u=>{t&&n&&(s=o(u),_f(t,n,s).then(f=>{Object.assign(n.style,{position:f.strategy,left:`${f.x}px`,top:`${f.y}px`}),s!=null&&s.onComputed&&s.onComputed(f)}))},a=u=>{if("subscribe"in u)return c(u),{};t=u,r()},l=(u,f)=>{let d;n=u,s=o(f),setTimeout(()=>r(f),0),r(f);const h=()=>{d&&(d(),d=void 0)},g=({autoUpdate:p}=s||{})=>{h(),p!==!1&&Ts().then(()=>mf(t,n,()=>r(s),p===!0?{}:p))};return d=g(),{update(p){r(p),d=g(p)},destroy(){h()}}},c=u=>{const f=u.subscribe(d=>{t===void 0?(t=d,r()):(Object.assign(t,d),r())});en(f)};return[a,l,r]}function yf(e){const t=e-1;return t*t*t+1}function $o(e,{delay:t=0,duration:n=400,easing:i=ta}={}){const s=+getComputedStyle(e).opacity;return{delay:t,duration:n,easing:i,css:o=>`opacity: ${o*s}`}}function Ao(e,{delay:t=0,duration:n=400,easing:i=yf,start:s=0,opacity:o=0}={}){const r=getComputedStyle(e),a=+r.opacity,l=r.transform==="none"?"":r.transform,c=1-s,u=a*(1-o);return{delay:t,duration:n,easing:i,css:(f,d)=>`
			transform: ${l} scale(${1-c*d});
			opacity: ${a-u*d}
		`}}function vf(e){let t,n,i,s,o;const r=e[2].default,a=At(r,e,e[1],null);return{c(){t=A("div"),a&&a.c(),t.hidden=!0},m(l,c){it(l,t,c),a&&a.m(t,null),i=!0,s||(o=Os(n=xf.call(null,t,e[0])),s=!0)},p(l,[c]){a&&a.p&&(!i||c&2)&&Ft(a,r,l,l[1],i?Pt(r,l[1],c,null):Dt(l[1]),null),n&&le(n.update)&&c&1&&n.update.call(null,l[0])},i(l){i||(S(a,l),i=!0)},o(l){C(a,l),i=!1},d(l){l&&U(t),a&&a.d(l),s=!1,o()}}}function xf(e,t="body"){let n;async function i(o){if(t=o,typeof t=="string"){if(n=document.querySelector(t),n===null&&(await Ts(),n=document.querySelector(t)),n===null)throw new Error(`No element found matching css selector: "${t}"`)}else if(t instanceof HTMLElement)n=t;else throw new TypeError(`Unknown portal target type: ${t===null?"null":typeof t}. Allowed types: string (CSS selector) or HTMLElement.`);n.appendChild(e),e.hidden=!1}function s(){e.parentNode&&e.parentNode.removeChild(e)}return i(t),{update:i,destroy:s}}function wf(e,t,n){let{$$slots:i={},$$scope:s}=t,{target:o="body"}=t;return e.$$set=r=>{"target"in r&&n(0,o=r.target),"$$scope"in r&&n(1,s=r.$$scope)},[o,s,i]}class Sf extends X{constructor(t){super(),Q(this,t,wf,vf,Y,{target:0})}}const Cf=e=>({}),Po=e=>({floatingRef:e[3],displayTooltip:e[5],hideTooltip:e[6]});function Fo(e){let t,n;return t=new Sf({props:{target:"body",$$slots:{default:[Mf]},$$scope:{ctx:e}}}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},p(i,s){const o={};s&257&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Mf(e){let t,n,i,s,o,r;return{c(){t=A("div"),n=J(e[0]),D(t,"class","absolute p-2 text-sm bg-dark-50 text-dark-400 rounded-md max-w-xl font-main")},m(a,l){it(a,t,l),w(t,n),s=!0,o||(r=Os(e[4].call(null,t)),o=!0)},p(a,l){(!s||l&1)&&Wt(n,a[0])},i(a){s||(Qe(()=>{s&&(i||(i=ai(t,$o,{duration:150},!0)),i.run(1))}),s=!0)},o(a){i||(i=ai(t,$o,{duration:150},!1)),i.run(0),s=!1},d(a){a&&U(t),a&&i&&i.end(),o=!1,r()}}}function kf(e){let t,n,i;const s=e[7].default,o=At(s,e,e[8],Po);let r=e[2]&&!e[1]&&Fo(e);return{c(){o&&o.c(),t=q(),r&&r.c(),n=kn()},m(a,l){o&&o.m(a,l),it(a,t,l),r&&r.m(a,l),it(a,n,l),i=!0},p(a,[l]){o&&o.p&&(!i||l&256)&&Ft(o,s,a,a[8],i?Pt(s,a[8],l,Cf):Dt(a[8]),Po),a[2]&&!a[1]?r?(r.p(a,l),l&6&&S(r,1)):(r=Fo(a),r.c(),S(r,1),r.m(n.parentNode,n)):r&&(kt(),C(r,1,1,()=>{r=null}),Rt())},i(a){i||(S(o,a),S(r),i=!0)},o(a){C(o,a),C(r),i=!1},d(a){o&&o.d(a),a&&U(t),r&&r.d(a),a&&U(n)}}}function Rf(e,t,n){let{$$slots:i={},$$scope:s}=t,{content:o}=t,{disabled:r}=t;const[a,l]=bf({strategy:"absolute",placement:"top",middleware:[uf(6),cf(),df()]});let c=!1,u;const f=()=>{r||(clearTimeout(u),u=setTimeout(()=>{n(2,c=!0)},300))},d=()=>{r||(clearTimeout(u),n(2,c=!1))};return e.$$set=h=>{"content"in h&&n(0,o=h.content),"disabled"in h&&n(1,r=h.disabled),"$$scope"in h&&n(8,s=h.$$scope)},[o,r,c,a,l,f,d,i,s]}class $f extends X{constructor(t){super(),Q(this,t,Rf,kf,Y,{content:0,disabled:1})}}function Do(e,t,n){const i=e.slice();return i[10]=t[n],i}function Oo(e,t,n){const i=e.slice();return i[13]=t[n],i}function Eo(e,t,n){const i=e.slice();return i[19]=t[n],i}function Lo(e,t,n){const i=e.slice();return i[22]=t[n],i}function To(e){let t,n,i,s,o,r,a,l,c,u;var f=fi(e[22].column.columnDef.header,e[22].getContext());function d(m){return{}}f&&(n=ye(f,d()));const h=[Pf,Af],g=[];function p(m,_){return _&1&&(s=null),_&1&&(o=null),s==null&&(s=m[22].column.getIsSorted()==="asc"),s?0:(o==null&&(o=m[22].column.getIsSorted()==="desc"),o?1:-1)}return~(r=p(e,-1))&&(a=g[r]=h[r](e)),{c(){t=A("button"),n&&V(n.$$.fragment),i=q(),a&&a.c(),D(t,"class","flex w-full items-center justify-center gap-1"),Fn(t,"cursor-pointer",e[22].column.getCanSort()),Fn(t,"select-none",e[22].column.getCanSort())},m(m,_){it(m,t,_),n&&E(n,t,null),w(t,i),~r&&g[r].m(t,null),l=!0,c||(u=It(t,"click",function(){le(e[22].column.getToggleSortingHandler())&&e[22].column.getToggleSortingHandler().apply(this,arguments)}),c=!0)},p(m,_){if(e=m,_&1&&f!==(f=fi(e[22].column.columnDef.header,e[22].getContext()))){if(n){kt();const y=n;C(y.$$.fragment,1,0,()=>{L(y,1)}),Rt()}f?(n=ye(f,d()),V(n.$$.fragment),S(n.$$.fragment,1),E(n,t,i)):n=null}let b=r;r=p(e,_),r!==b&&(a&&(kt(),C(g[b],1,1,()=>{g[b]=null}),Rt()),~r?(a=g[r],a||(a=g[r]=h[r](e),a.c()),S(a,1),a.m(t,null)):a=null),(!l||_&1)&&Fn(t,"cursor-pointer",e[22].column.getCanSort()),(!l||_&1)&&Fn(t,"select-none",e[22].column.getCanSort())},i(m){l||(n&&S(n.$$.fragment,m),S(a),l=!0)},o(m){n&&C(n.$$.fragment,m),C(a),l=!1},d(m){m&&U(t),n&&L(n),~r&&g[r].d(),c=!1,u()}}}function Af(e){let t,n;return t=new iu({}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Pf(e){let t,n;return t=new lu({}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Io(e){let t,n,i,s=!e[22].isPlaceholder&&To(e);return{c(){t=A("th"),s&&s.c(),D(t,"class",n=`bg-dark-600 select-none p-1 ${e[22].id==="executionTime"?"w-1/4":"w-3/4"}`)},m(o,r){it(o,t,r),s&&s.m(t,null),i=!0},p(o,r){o[22].isPlaceholder?s&&(kt(),C(s,1,1,()=>{s=null}),Rt()):s?(s.p(o,r),r&1&&S(s,1)):(s=To(o),s.c(),S(s,1),s.m(t,null)),(!i||r&1&&n!==(n=`bg-dark-600 select-none p-1 ${o[22].id==="executionTime"?"w-1/4":"w-3/4"}`))&&D(t,"class",n)},i(o){i||(S(s),i=!0)},o(o){C(s),i=!1},d(o){o&&U(t),s&&s.d()}}}function Vo(e){let t,n,i,s=e[19].headers,o=[];for(let a=0;a<s.length;a+=1)o[a]=Io(Lo(e,s,a));const r=a=>C(o[a],1,1,()=>{o[a]=null});return{c(){t=A("tr");for(let a=0;a<o.length;a+=1)o[a].c();n=q()},m(a,l){it(a,t,l);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);w(t,n),i=!0},p(a,l){if(l&1){s=a[19].headers;let c;for(c=0;c<s.length;c+=1){const u=Lo(a,s,c);o[c]?(o[c].p(u,l),S(o[c],1)):(o[c]=Io(u),o[c].c(),S(o[c],1),o[c].m(t,n))}for(kt(),c=s.length;c<o.length;c+=1)r(c);Rt()}},i(a){if(!i){for(let l=0;l<s.length;l+=1)S(o[l]);i=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)C(o[l]);i=!1},d(a){a&&U(t),Xe(o,a)}}}function Ff(e){let t,n,i,s,o,r;var a=fi(e[13].column.columnDef.cell,e[13].getContext());function l(c){return{}}return a&&(n=ye(a,l())),{c(){t=A("td"),n&&V(n.$$.fragment),D(t,"class",i=`${e[13].column.id==="executionTime"&&"text-center"} bg-dark-700 p-2 ${e[10].original.slow&&"text-yellow-500"} max-w-[200px] truncate`)},m(c,u){it(c,t,u),n&&E(n,t,null),s=!0,o||(r=[Os(e[16].call(null,t)),It(t,"mouseenter",function(){le(e[17])&&e[17].apply(this,arguments)}),It(t,"mouseleave",function(){le(e[18])&&e[18].apply(this,arguments)})],o=!0)},p(c,u){if(e=c,u&1&&a!==(a=fi(e[13].column.columnDef.cell,e[13].getContext()))){if(n){kt();const f=n;C(f.$$.fragment,1,0,()=>{L(f,1)}),Rt()}a?(n=ye(a,l()),V(n.$$.fragment),S(n.$$.fragment,1),E(n,t,null)):n=null}(!s||u&1&&i!==(i=`${e[13].column.id==="executionTime"&&"text-center"} bg-dark-700 p-2 ${e[10].original.slow&&"text-yellow-500"} max-w-[200px] truncate`))&&D(t,"class",i)},i(c){s||(n&&S(n.$$.fragment,c),s=!0)},o(c){n&&C(n.$$.fragment,c),s=!1},d(c){c&&U(t),n&&L(n),o=!1,te(r)}}}function zo(e){let t,n;return t=new $f({props:{content:e[13].getValue(),disabled:e[13].column.id!=="query",$$slots:{default:[Ff,({floatingRef:i,displayTooltip:s,hideTooltip:o})=>({16:i,17:s,18:o}),({floatingRef:i,displayTooltip:s,hideTooltip:o})=>(i?65536:0)|(s?131072:0)|(o?262144:0)]},$$scope:{ctx:e}}}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},p(i,s){const o={};s&1&&(o.content=i[13].getValue()),s&1&&(o.disabled=i[13].column.id!=="query"),s&33947649&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Ho(e){let t,n,i,s=e[10].getVisibleCells(),o=[];for(let a=0;a<s.length;a+=1)o[a]=zo(Oo(e,s,a));const r=a=>C(o[a],1,1,()=>{o[a]=null});return{c(){t=A("tr");for(let a=0;a<o.length;a+=1)o[a].c();n=q()},m(a,l){it(a,t,l);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);w(t,n),i=!0},p(a,l){if(l&393217){s=a[10].getVisibleCells();let c;for(c=0;c<s.length;c+=1){const u=Oo(a,s,c);o[c]?(o[c].p(u,l),S(o[c],1)):(o[c]=zo(u),o[c].c(),S(o[c],1),o[c].m(t,n))}for(kt(),c=s.length;c<o.length;c+=1)r(c);Rt()}},i(a){if(!i){for(let l=0;l<s.length;l+=1)S(o[l]);i=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)C(o[l]);i=!1},d(a){a&&U(t),Xe(o,a)}}}function Df(e){let t,n,i,s,o,r,a=e[0].getHeaderGroups(),l=[];for(let h=0;h<a.length;h+=1)l[h]=Vo(Eo(e,a,h));const c=h=>C(l[h],1,1,()=>{l[h]=null});let u=e[0].getRowModel().rows,f=[];for(let h=0;h<u.length;h+=1)f[h]=Ho(Do(e,u,h));const d=h=>C(f[h],1,1,()=>{f[h]=null});return{c(){t=A("div"),n=A("table"),i=A("thead");for(let h=0;h<l.length;h+=1)l[h].c();s=q(),o=A("tbody");for(let h=0;h<f.length;h+=1)f[h].c();D(i,"class","bg-dark-600"),D(n,"class","w-full"),D(t,"class","px-4")},m(h,g){it(h,t,g),w(t,n),w(n,i);for(let p=0;p<l.length;p+=1)l[p]&&l[p].m(i,null);w(n,s),w(n,o);for(let p=0;p<f.length;p+=1)f[p]&&f[p].m(o,null);r=!0},p(h,[g]){if(g&1){a=h[0].getHeaderGroups();let p;for(p=0;p<a.length;p+=1){const m=Eo(h,a,p);l[p]?(l[p].p(m,g),S(l[p],1)):(l[p]=Vo(m),l[p].c(),S(l[p],1),l[p].m(i,null))}for(kt(),p=a.length;p<l.length;p+=1)c(p);Rt()}if(g&393217){u=h[0].getRowModel().rows;let p;for(p=0;p<u.length;p+=1){const m=Do(h,u,p);f[p]?(f[p].p(m,g),S(f[p],1)):(f[p]=Ho(m),f[p].c(),S(f[p],1),f[p].m(o,null))}for(kt(),p=u.length;p<f.length;p+=1)d(p);Rt()}},i(h){if(!r){for(let g=0;g<a.length;g+=1)S(l[g]);for(let g=0;g<u.length;g+=1)S(f[g]);r=!0}},o(h){l=l.filter(Boolean);for(let g=0;g<l.length;g+=1)C(l[g]);f=f.filter(Boolean);for(let g=0;g<f.length;g+=1)C(f[g]);r=!1},d(h){h&&U(t),Xe(l,h),Xe(f,h)}}}function Of(e,t,n){let i,s,o;gt(e,Bt,h=>n(4,i=h)),gt(e,Qn,h=>n(5,s=h));const r=zs(),a=[{accessorKey:"query",header:"Query",cell:h=>h.getValue(),enableSorting:!0},{accessorKey:"executionTime",header:"Time (ms)",cell:h=>h.getValue().toFixed(4),enableSorting:!0}];let l=[];const c=h=>{h instanceof Function?n(2,l=h(l)):n(2,l=h),u.update(g=>({...g,state:{...g.state,sorting:l}}))},u=Ot({data:s,columns:a,manualPagination:!0,manualSorting:!0,pageCount:-1,getCoreRowModel:Bu(),onSortingChange:c,state:{sorting:l}}),f=Ju(u);gt(e,f,h=>n(0,o=h));let d;return e.$$.update=()=>{e.$$.dirty&32&&u.update(h=>({...h,data:s})),e.$$.dirty&28&&(clearTimeout(d),n(3,d=setTimeout(()=>{ba("fetchResource",{resource:r.params.resource,pageIndex:i.page,search:i.search,sortBy:l})},300)))},[o,f,l,d,i,s]}class Ef extends X{constructor(t){super(),Q(this,t,Of,Df,Y,{})}}function Lf(e){let t,n,i,s,o,r,a,l,c,u=e[0].resourceQueriesCount+"",f,d,h,g,p=e[0].resourceTime.toFixed(4)+"",m,_,b,y,x,v=e[0].resourceSlowQueries+"",R,k,$,P;return i=new ya({}),{c(){t=A("div"),n=A("button"),V(i.$$.fragment),s=q(),o=A("p"),o.textContent=`${e[1].params.resource}`,r=q(),a=A("div"),l=A("p"),c=J("Queries: "),f=J(u),d=q(),h=A("p"),g=J("Time: "),m=J(p),_=J(" ms"),b=q(),y=A("p"),x=J("Slow queries: "),R=J(v),D(n,"class","flex p-2 w-12 bg-dark-600 text-dark-100 hover:text-white rounded-md justify-center items-center hover:bg-dark-500 outline-none border-[1px] border-transparent focus-visible:border-cyan-600"),D(o,"class","text-center text-lg"),D(y,"class","text-yellow-500"),D(a,"class","text-end text-dark-100 flex flex-col text-xs"),D(t,"class","p-4 grid grid-flow-col grid-cols-3 items-center ")},m(F,M){it(F,t,M),w(t,n),E(i,n,null),w(t,s),w(t,o),w(t,r),w(t,a),w(a,l),w(l,c),w(l,f),w(a,d),w(a,h),w(h,g),w(h,m),w(h,_),w(a,b),w(a,y),w(y,x),w(y,R),k=!0,$||(P=It(n,"click",e[2]),$=!0)},p(F,[M]){(!k||M&1)&&u!==(u=F[0].resourceQueriesCount+"")&&Wt(f,u),(!k||M&1)&&p!==(p=F[0].resourceTime.toFixed(4)+"")&&Wt(m,p),(!k||M&1)&&v!==(v=F[0].resourceSlowQueries+"")&&Wt(R,v)},i(F){k||(S(i.$$.fragment,F),k=!0)},o(F){C(i.$$.fragment,F),k=!1},d(F){F&&U(t),L(i),$=!1,P()}}}function Tf(e,t,n){let i;gt(e,ps,r=>n(0,i=r));const s=zs();return[i,s,()=>Ee.goto("/")]}class If extends X{constructor(t){super(),Q(this,t,Tf,Lf,Y,{})}}function za(e,t){const n=i=>{const{action:s,data:o}=i.data;s===e&&t(o)};Ai(()=>window.addEventListener("message",n)),en(()=>window.removeEventListener("message",n))}const Ha=()=>!window.invokeNative,Na=(e,t=1e3)=>{if(Ha())for(const n of e)setTimeout(()=>{window.dispatchEvent(new MessageEvent("message",{data:{action:n.action,data:n.data}}))},t)};function Vf(e){let t,n,i,s,o,r,a,l;var c=e[0];function u(f){return{props:{class:"text-dark-300"}}}return c&&(i=ye(c,u())),{c(){t=A("div"),n=A("div"),i&&V(i.$$.fragment),s=q(),o=A("input"),D(n,"class","pr-2"),D(o,"type","text"),D(o,"class","w-full bg-transparent outline-none"),D(o,"placeholder","Search queries..."),D(t,"class","bg-dark-600 m-4 mt-0 flex items-center rounded-md border-[1px] border-transparent p-2 outline-none transition-all duration-100 focus-within:border-cyan-600")},m(f,d){it(f,t,d),w(t,n),i&&E(i,n,null),w(t,s),w(t,o),si(o,e[1]),r=!0,a||(l=It(o,"input",e[2]),a=!0)},p(f,[d]){if(d&1&&c!==(c=f[0])){if(i){kt();const h=i;C(h.$$.fragment,1,0,()=>{L(h,1)}),Rt()}c?(i=ye(c,u()),V(i.$$.fragment),S(i.$$.fragment,1),E(i,n,null)):i=null}d&2&&o.value!==f[1]&&si(o,f[1])},i(f){r||(i&&S(i.$$.fragment,f),r=!0)},o(f){i&&C(i.$$.fragment,f),r=!1},d(f){f&&U(t),i&&L(i),a=!1,l()}}}function zf(e,t,n){let i;gt(e,Bt,a=>n(3,i=a));let s="",{icon:o}=t;function r(){s=this.value,n(1,s)}return e.$$set=a=>{"icon"in a&&n(0,o=a.icon)},e.$$.update=()=>{e.$$.dirty&2&&(bt(Bt,i.search=s,i),bt(Bt,i.page=0,i))},[o,s,r]}class Hf extends X{constructor(t){super(),Q(this,t,zf,Vf,Y,{icon:0})}}function Nf(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Bf(e){let t,n;const i=[{name:"search"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Nf]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function jf(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"}],["path",{d:"M21 21l-6 -6"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class Wf extends X{constructor(t){super(),Q(this,t,jf,Bf,Y,{})}}const Ba=Wf;function Gf(e){let t,n,i,s,o,r,a,l,c,u;return i=new If({}),o=new Hf({props:{icon:Ba}}),a=new Ef({}),c=new Zc({props:{maxPage:e[0]}}),{c(){t=A("div"),n=A("div"),V(i.$$.fragment),s=q(),V(o.$$.fragment),r=q(),V(a.$$.fragment),l=q(),V(c.$$.fragment),D(t,"class","flex w-full flex-col justify-between")},m(f,d){it(f,t,d),w(t,n),E(i,n,null),w(n,s),E(o,n,null),w(n,r),E(a,n,null),w(t,l),E(c,t,null),u=!0},p(f,[d]){const h={};d&1&&(h.maxPage=f[0]),c.$set(h)},i(f){u||(S(i.$$.fragment,f),S(o.$$.fragment,f),S(a.$$.fragment,f),S(c.$$.fragment,f),u=!0)},o(f){C(i.$$.fragment,f),C(o.$$.fragment,f),C(a.$$.fragment,f),C(c.$$.fragment,f),u=!1},d(f){f&&U(t),L(i),L(o),L(a),L(c)}}}function qf(e,t,n){let i,s,o;gt(e,ps,a=>n(1,i=a)),gt(e,Qn,a=>n(2,s=a)),gt(e,Bt,a=>n(3,o=a));let r=0;return en(()=>{bt(Qn,s=[],s),bt(Bt,o.page=0,o)}),Na([{action:"loadResource",data:{queries:[{query:"SELECT * FROM users WHERE ID = 1",executionTime:3,slow:!1,date:Date.now()},{query:"SELECT * FROM users WHERE ID = 1",executionTime:23,slow:!0,date:Date.now()},{query:"SELECT * FROM users WHERE ID = 1",executionTime:15,slow:!1,date:Date.now()},{query:"SELECT * FROM users WHERE ID = 1",executionTime:122,slow:!0,date:Date.now()}],resourceQueriesCount:3,resourceSlowQueries:2,resourceTime:1342,pageCount:3}}]),za("loadResource",a=>{n(0,r=a.pageCount),bt(Qn,s=a.queries,s),bt(ps,i={resourceQueriesCount:a.resourceQueriesCount,resourceSlowQueries:a.resourceSlowQueries,resourceTime:a.resourceTime},i)}),[r]}class Yf extends X{constructor(t){super(),Q(this,t,qf,Gf,Y,{})}}function Uf(e){let t,n,i,s,o,r,a,l;var c=e[1];function u(f){return{props:{class:"text-dark-300"}}}return c&&(i=ye(c,u())),{c(){t=A("div"),n=A("div"),i&&V(i.$$.fragment),s=q(),o=A("input"),D(n,"class","pr-2"),D(o,"type","text"),D(o,"class","bg-transparent outline-none w-full"),D(o,"placeholder","Search resources..."),D(t,"class","p-2 flex items-center outline-none border-[1px] border-transparent transition-all duration-100 focus-within:border-cyan-600 rounded-md bg-dark-600")},m(f,d){it(f,t,d),w(t,n),i&&E(i,n,null),w(t,s),w(t,o),si(o,e[0]),r=!0,a||(l=It(o,"input",e[2]),a=!0)},p(f,[d]){if(d&2&&c!==(c=f[1])){if(i){kt();const h=i;C(h.$$.fragment,1,0,()=>{L(h,1)}),Rt()}c?(i=ye(c,u()),V(i.$$.fragment),S(i.$$.fragment,1),E(i,n,null)):i=null}d&1&&o.value!==f[0]&&si(o,f[0])},i(f){r||(i&&S(i.$$.fragment,f),r=!0)},o(f){i&&C(i.$$.fragment,f),r=!1},d(f){f&&U(t),i&&L(i),a=!1,l()}}}function Xf(e,t,n){let{icon:i}=t,{value:s}=t;function o(){s=this.value,n(0,s)}return e.$$set=r=>{"icon"in r&&n(1,i=r.icon),"value"in r&&n(0,s=r.value)},[s,i,o]}class Kf extends X{constructor(t){super(),Q(this,t,Xf,Uf,Y,{icon:1,value:0})}}function Qf(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Zf(e){let t,n;const i=[{name:"file-analytics"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Qf]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Jf(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"}],["path",{d:"M9 17l0 -5"}],["path",{d:"M12 17l0 -1"}],["path",{d:"M15 17l0 -3"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class td extends X{constructor(t){super(),Q(this,t,Jf,Zf,Y,{})}}const ed=td;function nd(e){let t;const n=e[2].default,i=At(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ft(i,n,s,s[3],t?Pt(n,s[3],o,null):Dt(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function id(e){let t,n;const i=[{name:"source-code"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[nd]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=N(s,i[o]);return t=new ce({props:s}),{c(){V(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&ee(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function sd(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M14.5 4h2.5a3 3 0 0 1 3 3v10a3 3 0 0 1 -3 3h-10a3 3 0 0 1 -3 -3v-5"}],["path",{d:"M6 5l-2 2l2 2"}],["path",{d:"M10 9l2 -2l-2 -2"}]];return e.$$set=r=>{n(1,t=N(N({},t),nt(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=nt(t),[o,t,i,s]}class od extends X{constructor(t){super(),Q(this,t,sd,id,Y,{})}}const rd=od;/*!
 * @kurkle/color v0.3.2
 * https://github.com/kurkle/color#readme
 * (c) 2023 Jukka Kurkela
 * Released under the MIT License
 */function Rn(e){return e+.5|0}const ge=(e,t,n)=>Math.max(Math.min(e,n),t);function un(e){return ge(Rn(e*2.55),0,255)}function be(e){return ge(Rn(e*255),0,255)}function re(e){return ge(Rn(e/2.55)/100,0,1)}function No(e){return ge(Rn(e*100),0,100)}const Tt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ws=[..."0123456789ABCDEF"],ad=e=>ws[e&15],ld=e=>ws[(e&240)>>4]+ws[e&15],Tn=e=>(e&240)>>4===(e&15),cd=e=>Tn(e.r)&&Tn(e.g)&&Tn(e.b)&&Tn(e.a);function ud(e){var t=e.length,n;return e[0]==="#"&&(t===4||t===5?n={r:255&Tt[e[1]]*17,g:255&Tt[e[2]]*17,b:255&Tt[e[3]]*17,a:t===5?Tt[e[4]]*17:255}:(t===7||t===9)&&(n={r:Tt[e[1]]<<4|Tt[e[2]],g:Tt[e[3]]<<4|Tt[e[4]],b:Tt[e[5]]<<4|Tt[e[6]],a:t===9?Tt[e[7]]<<4|Tt[e[8]]:255})),n}const fd=(e,t)=>e<255?t(e):"";function dd(e){var t=cd(e)?ad:ld;return e?"#"+t(e.r)+t(e.g)+t(e.b)+fd(e.a,t):void 0}const hd=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ja(e,t,n){const i=t*Math.min(n,1-n),s=(o,r=(o+e/30)%12)=>n-i*Math.max(Math.min(r-3,9-r,1),-1);return[s(0),s(8),s(4)]}function gd(e,t,n){const i=(s,o=(s+e/60)%6)=>n-n*t*Math.max(Math.min(o,4-o,1),0);return[i(5),i(3),i(1)]}function pd(e,t,n){const i=ja(e,1,.5);let s;for(t+n>1&&(s=1/(t+n),t*=s,n*=s),s=0;s<3;s++)i[s]*=1-t-n,i[s]+=t;return i}function md(e,t,n,i,s){return e===s?(t-n)/i+(t<n?6:0):t===s?(n-e)/i+2:(e-t)/i+4}function Ws(e){const n=e.r/255,i=e.g/255,s=e.b/255,o=Math.max(n,i,s),r=Math.min(n,i,s),a=(o+r)/2;let l,c,u;return o!==r&&(u=o-r,c=a>.5?u/(2-o-r):u/(o+r),l=md(n,i,s,u,o),l=l*60+.5),[l|0,c||0,a]}function Gs(e,t,n,i){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,i)).map(be)}function qs(e,t,n){return Gs(ja,e,t,n)}function _d(e,t,n){return Gs(pd,e,t,n)}function bd(e,t,n){return Gs(gd,e,t,n)}function Wa(e){return(e%360+360)%360}function yd(e){const t=hd.exec(e);let n=255,i;if(!t)return;t[5]!==i&&(n=t[6]?un(+t[5]):be(+t[5]));const s=Wa(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?i=_d(s,o,r):t[1]==="hsv"?i=bd(s,o,r):i=qs(s,o,r),{r:i[0],g:i[1],b:i[2],a:n}}function vd(e,t){var n=Ws(e);n[0]=Wa(n[0]+t),n=qs(n),e.r=n[0],e.g=n[1],e.b=n[2]}function xd(e){if(!e)return;const t=Ws(e),n=t[0],i=No(t[1]),s=No(t[2]);return e.a<255?`hsla(${n}, ${i}%, ${s}%, ${re(e.a)})`:`hsl(${n}, ${i}%, ${s}%)`}const Bo={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},jo={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function wd(){const e={},t=Object.keys(jo),n=Object.keys(Bo);let i,s,o,r,a;for(i=0;i<t.length;i++){for(r=a=t[i],s=0;s<n.length;s++)o=n[s],a=a.replace(o,Bo[o]);o=parseInt(jo[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}let In;function Sd(e){In||(In=wd(),In.transparent=[0,0,0,0]);const t=In[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Cd=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Md(e){const t=Cd.exec(e);let n=255,i,s,o;if(t){if(t[7]!==i){const r=+t[7];n=t[8]?un(r):ge(r*255,0,255)}return i=+t[1],s=+t[3],o=+t[5],i=255&(t[2]?un(i):ge(i,0,255)),s=255&(t[4]?un(s):ge(s,0,255)),o=255&(t[6]?un(o):ge(o,0,255)),{r:i,g:s,b:o,a:n}}}function kd(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${re(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const Qi=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,Ne=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Rd(e,t,n){const i=Ne(re(e.r)),s=Ne(re(e.g)),o=Ne(re(e.b));return{r:be(Qi(i+n*(Ne(re(t.r))-i))),g:be(Qi(s+n*(Ne(re(t.g))-s))),b:be(Qi(o+n*(Ne(re(t.b))-o))),a:e.a+n*(t.a-e.a)}}function Vn(e,t,n){if(e){let i=Ws(e);i[t]=Math.max(0,Math.min(i[t]+i[t]*n,t===0?360:1)),i=qs(i),e.r=i[0],e.g=i[1],e.b=i[2]}}function Ga(e,t){return e&&Object.assign(t||{},e)}function Wo(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=be(e[3]))):(t=Ga(e,{r:0,g:0,b:0,a:1}),t.a=be(t.a)),t}function $d(e){return e.charAt(0)==="r"?Md(e):yd(e)}class xn{constructor(t){if(t instanceof xn)return t;const n=typeof t;let i;n==="object"?i=Wo(t):n==="string"&&(i=ud(t)||Sd(t)||$d(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=Ga(this._rgb);return t&&(t.a=re(t.a)),t}set rgb(t){this._rgb=Wo(t)}rgbString(){return this._valid?kd(this._rgb):void 0}hexString(){return this._valid?dd(this._rgb):void 0}hslString(){return this._valid?xd(this._rgb):void 0}mix(t,n){if(t){const i=this.rgb,s=t.rgb;let o;const r=n===o?.5:n,a=2*r-1,l=i.a-s.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,i.r=255&c*i.r+o*s.r+.5,i.g=255&c*i.g+o*s.g+.5,i.b=255&c*i.b+o*s.b+.5,i.a=r*i.a+(1-r)*s.a,this.rgb=i}return this}interpolate(t,n){return t&&(this._rgb=Rd(this._rgb,t._rgb,n)),this}clone(){return new xn(this.rgb)}alpha(t){return this._rgb.a=be(t),this}clearer(t){const n=this._rgb;return n.a*=1-t,this}greyscale(){const t=this._rgb,n=Rn(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=n,this}opaquer(t){const n=this._rgb;return n.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Vn(this._rgb,2,t),this}darken(t){return Vn(this._rgb,2,-t),this}saturate(t){return Vn(this._rgb,1,t),this}desaturate(t){return Vn(this._rgb,1,-t),this}rotate(t){return vd(this._rgb,t),this}}/*!
 * Chart.js v4.2.1
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */function ie(){}const Ad=(()=>{let e=0;return()=>e++})();function tt(e){return e===null||typeof e>"u"}function ot(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function W(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function dt(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function St(e,t){return dt(e)?e:t}function et(e,t){return typeof e>"u"?t:e}const Pd=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/t,qa=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*t:+e;function rt(e,t,n){if(e&&typeof e.call=="function")return e.apply(n,t)}function G(e,t,n,i){let s,o,r;if(ot(e))if(o=e.length,i)for(s=o-1;s>=0;s--)t.call(n,e[s],s);else for(s=0;s<o;s++)t.call(n,e[s],s);else if(W(e))for(r=Object.keys(e),o=r.length,s=0;s<o;s++)t.call(n,e[r[s]],r[s])}function pi(e,t){let n,i,s,o;if(!e||!t||e.length!==t.length)return!1;for(n=0,i=e.length;n<i;++n)if(s=e[n],o=t[n],s.datasetIndex!==o.datasetIndex||s.index!==o.index)return!1;return!0}function mi(e){if(ot(e))return e.map(mi);if(W(e)){const t=Object.create(null),n=Object.keys(e),i=n.length;let s=0;for(;s<i;++s)t[n[s]]=mi(e[n[s]]);return t}return e}function Ya(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function Fd(e,t,n,i){if(!Ya(e))return;const s=t[e],o=n[e];W(s)&&W(o)?wn(s,o,i):t[e]=mi(o)}function wn(e,t,n){const i=ot(t)?t:[t],s=i.length;if(!W(e))return e;n=n||{};const o=n.merger||Fd;let r;for(let a=0;a<s;++a){if(r=i[a],!W(r))continue;const l=Object.keys(r);for(let c=0,u=l.length;c<u;++c)o(l[c],e,r,n)}return e}function pn(e,t){return wn(e,t,{merger:Dd})}function Dd(e,t,n){if(!Ya(e))return;const i=t[e],s=n[e];W(i)&&W(s)?pn(i,s):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=mi(s))}const Go={"":e=>e,x:e=>e.x,y:e=>e.y};function Od(e){const t=e.split("."),n=[];let i="";for(const s of t)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(n.push(i),i="");return n}function Ed(e){const t=Od(e);return n=>{for(const i of t){if(i==="")break;n=n&&n[i]}return n}}function Ze(e,t){return(Go[t]||(Go[t]=Ed(t)))(e)}function Ys(e){return e.charAt(0).toUpperCase()+e.slice(1)}const Ut=e=>typeof e<"u",we=e=>typeof e=="function",qo=(e,t)=>{if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0};function Ld(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}const ct=Math.PI,at=2*ct,_i=Number.POSITIVE_INFINITY,Td=ct/180,lt=ct/2,Me=ct/4,Yo=ct*2/3,pe=Math.log10,bi=Math.sign;function Zn(e,t,n){return Math.abs(e-t)<n}function Uo(e){const t=Math.round(e);e=Zn(e,t,e/1e3)?t:e;const n=Math.pow(10,Math.floor(pe(e))),i=e/n;return(i<=1?1:i<=2?2:i<=5?5:10)*n}function Id(e){const t=[],n=Math.sqrt(e);let i;for(i=1;i<n;i++)e%i===0&&(t.push(i),t.push(e/i));return n===(n|0)&&t.push(n),t.sort((s,o)=>s-o).pop(),t}function yi(e){return!isNaN(parseFloat(e))&&isFinite(e)}function Vd(e,t){const n=Math.round(e);return n-t<=e&&n+t>=e}function Ua(e,t,n){let i,s,o;for(i=0,s=e.length;i<s;i++)o=e[i][n],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function jt(e){return e*(ct/180)}function Us(e){return e*(180/ct)}function Xo(e){if(!dt(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function Xa(e,t){const n=t.x-e.x,i=t.y-e.y,s=Math.sqrt(n*n+i*i);let o=Math.atan2(i,n);return o<-.5*ct&&(o+=at),{angle:o,distance:s}}function zd(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function Zt(e){return(e%at+at)%at}function vi(e,t,n,i){const s=Zt(e),o=Zt(t),r=Zt(n),a=Zt(o-s),l=Zt(r-s),c=Zt(s-o),u=Zt(s-r);return s===o||s===r||i&&o===r||a>l&&c<u}function Mt(e,t,n){return Math.max(t,Math.min(n,e))}function Hd(e){return Mt(e,-32768,32767)}function Nd(e,t,n,i=1e-6){return e>=Math.min(t,n)-i&&e<=Math.max(t,n)+i}function Xs(e,t,n){n=n||(r=>e[r]<t);let i=e.length-1,s=0,o;for(;i-s>1;)o=s+i>>1,n(o)?s=o:i=o;return{lo:s,hi:i}}const Ss=(e,t,n,i)=>Xs(e,n,i?s=>{const o=e[s][t];return o<n||o===n&&e[s+1][t]===n}:s=>e[s][t]<n),Bd=(e,t,n)=>Xs(e,n,i=>e[i][t]>=n);function jd(e,t,n){let i=0,s=e.length;for(;i<s&&e[i]<t;)i++;for(;s>i&&e[s-1]>n;)s--;return i>0||s<e.length?e.slice(i,s):e}const Ka=["push","pop","shift","splice","unshift"];function Wd(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ka.forEach(n=>{const i="_onData"+Ys(n),s=e[n];Object.defineProperty(e,n,{configurable:!0,enumerable:!1,value(...o){const r=s.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...o)}),r}})})}function Ko(e,t){const n=e._chartjs;if(!n)return;const i=n.listeners,s=i.indexOf(t);s!==-1&&i.splice(s,1),!(i.length>0)&&(Ka.forEach(o=>{delete e[o]}),delete e._chartjs)}function Gd(e){const t=new Set;let n,i;for(n=0,i=e.length;n<i;++n)t.add(e[n]);return t.size===i?e:Array.from(t)}const Qa=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function Za(e,t){let n=[],i=!1;return function(...s){n=s,i||(i=!0,Qa.call(window,()=>{i=!1,e.apply(t,n)}))}}function qd(e,t){let n;return function(...i){return t?(clearTimeout(n),n=setTimeout(e,t,i)):e.apply(this,i),t}}const Ja=e=>e==="start"?"left":e==="end"?"right":"center",mn=(e,t,n)=>e==="start"?t:e==="end"?n:(t+n)/2,zn=e=>e===0||e===1,Qo=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*at/n)),Zo=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*at/n)+1,_n={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*lt)+1,easeOutSine:e=>Math.sin(e*lt),easeInOutSine:e=>-.5*(Math.cos(ct*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>zn(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>zn(e)?e:Qo(e,.075,.3),easeOutElastic:e=>zn(e)?e:Zo(e,.075,.3),easeInOutElastic(e){return zn(e)?e:e<.5?.5*Qo(e*2,.1125,.45):.5+.5*Zo(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-_n.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?_n.easeInBounce(e*2)*.5:_n.easeOutBounce(e*2-1)*.5+.5};function tl(e){if(e&&typeof e=="object"){const t=e.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Jo(e){return tl(e)?e:new xn(e)}function Zi(e){return tl(e)?e:new xn(e).saturate(.5).darken(.1).hexString()}const Yd=["x","y","borderWidth","radius","tension"],Ud=["color","borderColor","backgroundColor"];function Xd(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),e.set("animations",{colors:{type:"color",properties:Ud},numbers:{type:"number",properties:Yd}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Kd(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const tr=new Map;function Qd(e,t){t=t||{};const n=e+JSON.stringify(t);let i=tr.get(n);return i||(i=new Intl.NumberFormat(e,t),tr.set(n,i)),i}function $n(e,t,n){return Qd(t,n).format(e)}const el={values(e){return ot(e)?e:""+e},numeric(e,t,n){if(e===0)return"0";const i=this.chart.options.locale;let s,o=e;if(n.length>1){const c=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),o=Zd(e,n)}const r=pe(Math.abs(o)),a=Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),$n(e,i,l)},logarithmic(e,t,n){if(e===0)return"0";const i=n[t].significand||e/Math.pow(10,Math.floor(pe(e)));return[1,2,3,5,10,15].includes(i)||t>.8*n.length?el.numeric.call(this,e,t,n):""}};function Zd(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e)),n}var Ei={formatters:el};function Jd(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,n)=>n.lineWidth,tickColor:(t,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ei.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Te=Object.create(null),Cs=Object.create(null);function bn(e,t){if(!t)return e;const n=t.split(".");for(let i=0,s=n.length;i<s;++i){const o=n[i];e=e[o]||(e[o]=Object.create(null))}return e}function Ji(e,t,n){return typeof t=="string"?wn(bn(e,t),n):wn(bn(e,""),t)}class th{constructor(t,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>Zi(s.backgroundColor),this.hoverBorderColor=(i,s)=>Zi(s.borderColor),this.hoverColor=(i,s)=>Zi(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(n)}set(t,n){return Ji(this,t,n)}get(t){return bn(this,t)}describe(t,n){return Ji(Cs,t,n)}override(t,n){return Ji(Te,t,n)}route(t,n,i,s){const o=bn(this,t),r=bn(this,i),a="_"+n;Object.defineProperties(o,{[a]:{value:o[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],c=r[s];return W(l)?Object.assign({},c,l):et(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(n=>n(this))}}var ut=new th({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Xd,Kd,Jd]);function eh(e){return!e||tt(e.size)||tt(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function xi(e,t,n,i,s){let o=t[s];return o||(o=t[s]=e.measureText(s).width,n.push(s)),o>i&&(i=o),i}function nh(e,t,n,i){i=i||{};let s=i.data=i.data||{},o=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(s=i.data={},o=i.garbageCollect=[],i.font=t),e.save(),e.font=t;let r=0;const a=n.length;let l,c,u,f,d;for(l=0;l<a;l++)if(f=n[l],f!=null&&ot(f)!==!0)r=xi(e,s,o,r,f);else if(ot(f))for(c=0,u=f.length;c<u;c++)d=f[c],d!=null&&!ot(d)&&(r=xi(e,s,o,r,d));e.restore();const h=o.length/2;if(h>n.length){for(l=0;l<h;l++)delete s[o[l]];o.splice(0,h)}return r}function ke(e,t,n){const i=e.currentDevicePixelRatio,s=n!==0?Math.max(n/2,.5):0;return Math.round((t-s)*i)/i+s}function er(e,t){t=t||e.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore()}function nr(e,t,n,i){ih(e,t,n,i,null)}function ih(e,t,n,i,s){let o,r,a,l,c,u,f,d;const h=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*Td;if(h&&typeof h=="object"&&(o=h.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(n,i),e.rotate(m),e.drawImage(h,-h.width/2,-h.height/2,h.width,h.height),e.restore();return}if(!(isNaN(p)||p<=0)){switch(e.beginPath(),h){default:s?e.ellipse(n,i,s/2,p,0,0,at):e.arc(n,i,p,0,at),e.closePath();break;case"triangle":u=s?s/2:p,e.moveTo(n+Math.sin(m)*u,i-Math.cos(m)*p),m+=Yo,e.lineTo(n+Math.sin(m)*u,i-Math.cos(m)*p),m+=Yo,e.lineTo(n+Math.sin(m)*u,i-Math.cos(m)*p),e.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+Me)*l,f=Math.cos(m+Me)*(s?s/2-c:l),a=Math.sin(m+Me)*l,d=Math.sin(m+Me)*(s?s/2-c:l),e.arc(n-f,i-a,c,m-ct,m-lt),e.arc(n+d,i-r,c,m-lt,m),e.arc(n+f,i+a,c,m,m+lt),e.arc(n-d,i+r,c,m+lt,m+ct),e.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,u=s?s/2:l,e.rect(n-u,i-l,2*u,2*l);break}m+=Me;case"rectRot":f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+d,i-r),e.lineTo(n+f,i+a),e.lineTo(n-d,i+r),e.closePath();break;case"crossRot":m+=Me;case"cross":f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+f,i+a),e.moveTo(n+d,i-r),e.lineTo(n-d,i+r);break;case"star":f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+f,i+a),e.moveTo(n+d,i-r),e.lineTo(n-d,i+r),m+=Me,f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+f,i+a),e.moveTo(n+d,i-r),e.lineTo(n-d,i+r);break;case"line":r=s?s/2:Math.cos(m)*p,a=Math.sin(m)*p,e.moveTo(n-r,i-a),e.lineTo(n+r,i+a);break;case"dash":e.moveTo(n,i),e.lineTo(n+Math.cos(m)*(s?s/2:p),i+Math.sin(m)*p);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function nl(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function il(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function sl(e){e.restore()}function Sn(e,t,n,i,s,o={}){const r=ot(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(e.save(),e.font=s.string,sh(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&rh(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),tt(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,n,i,o.maxWidth)),e.fillText(c,n,i,o.maxWidth),oh(e,n,i,c,o),i+=s.lineHeight;e.restore()}function sh(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),tt(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function oh(e,t,n,i,s){if(s.strikethrough||s.underline){const o=e.measureText(i),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=n-o.actualBoundingBoxAscent,c=n+o.actualBoundingBoxDescent,u=s.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=s.decorationWidth||2,e.moveTo(r,u),e.lineTo(a,u),e.stroke()}}function rh(e,t){const n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function Ms(e,t){const{x:n,y:i,w:s,h:o,radius:r}=t;e.arc(n+r.topLeft,i+r.topLeft,r.topLeft,-lt,ct,!0),e.lineTo(n,i+o-r.bottomLeft),e.arc(n+r.bottomLeft,i+o-r.bottomLeft,r.bottomLeft,ct,lt,!0),e.lineTo(n+s-r.bottomRight,i+o),e.arc(n+s-r.bottomRight,i+o-r.bottomRight,r.bottomRight,lt,0,!0),e.lineTo(n+s,i+r.topRight),e.arc(n+s-r.topRight,i+r.topRight,r.topRight,0,-lt,!0),e.lineTo(n+r.topLeft,i)}const ah=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,lh=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function ch(e,t){const n=(""+e).match(ah);if(!n||n[1]==="normal")return t*1.2;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100;break}return t*e}const uh=e=>+e||0;function Ks(e,t){const n={},i=W(t),s=i?Object.keys(t):t,o=W(e)?i?r=>et(e[r],e[t[r]]):r=>e[r]:()=>e;for(const r of s)n[r]=uh(o(r));return n}function fh(e){return Ks(e,{top:"y",right:"x",bottom:"y",left:"x"})}function yn(e){return Ks(e,["topLeft","topRight","bottomLeft","bottomRight"])}function $t(e){const t=fh(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function yt(e,t){e=e||{},t=t||ut.font;let n=et(e.size,t.size);typeof n=="string"&&(n=parseInt(n,10));let i=et(e.style,t.style);i&&!(""+i).match(lh)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:et(e.family,t.family),lineHeight:ch(et(e.lineHeight,t.lineHeight),n),size:n,style:i,weight:et(e.weight,t.weight),string:""};return s.string=eh(s),s}function Hn(e,t,n,i){let s=!0,o,r,a;for(o=0,r=e.length;o<r;++o)if(a=e[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),s=!1),n!==void 0&&ot(a)&&(a=a[n%a.length],s=!1),a!==void 0))return i&&!s&&(i.cacheable=!1),a}function dh(e,t,n){const{min:i,max:s}=e,o=qa(t,(s-i)/2),r=(a,l)=>n&&a===0?0:a+l;return{min:r(i,-Math.abs(o)),max:r(s,o)}}function Ie(e,t){return Object.assign(Object.create(e),t)}function Qs(e,t=[""],n=e,i,s=()=>e[0]){Ut(i)||(i=ll("_fallback",e));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:n,_fallback:i,_getTarget:s,override:r=>Qs([r,...e],t,n,i)};return new Proxy(o,{deleteProperty(r,a){return delete r[a],delete r._keys,delete e[0][a],!0},get(r,a){return rl(r,a,()=>vh(a,t,e,r))},getOwnPropertyDescriptor(r,a){return Reflect.getOwnPropertyDescriptor(r._scopes[0],a)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(r,a){return sr(r).includes(a)},ownKeys(r){return sr(r)},set(r,a,l){const c=r._storage||(r._storage=s());return r[a]=c[a]=l,delete r._keys,!0}})}function Je(e,t,n,i){const s={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:ol(e,i),setContext:o=>Je(e,o,n,i),override:o=>Je(e.override(o),t,n,i)};return new Proxy(s,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return rl(o,r,()=>gh(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function ol(e,t={scriptable:!0,indexable:!0}){const{_scriptable:n=t.scriptable,_indexable:i=t.indexable,_allKeys:s=t.allKeys}=e;return{allKeys:s,scriptable:n,indexable:i,isScriptable:we(n)?n:()=>n,isIndexable:we(i)?i:()=>i}}const hh=(e,t)=>e?e+Ys(t):t,Zs=(e,t)=>W(t)&&e!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function rl(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t))return e[t];const i=n();return e[t]=i,i}function gh(e,t,n){const{_proxy:i,_context:s,_subProxy:o,_descriptors:r}=e;let a=i[t];return we(a)&&r.isScriptable(t)&&(a=ph(t,a,e,n)),ot(a)&&a.length&&(a=mh(t,a,e,r.isIndexable)),Zs(t,a)&&(a=Je(a,s,o&&o[t],r)),a}function ph(e,t,n,i){const{_proxy:s,_context:o,_subProxy:r,_stack:a}=n;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);return a.add(e),t=t(o,r||i),a.delete(e),Zs(e,t)&&(t=Js(s._scopes,s,e,t)),t}function mh(e,t,n,i){const{_proxy:s,_context:o,_subProxy:r,_descriptors:a}=n;if(Ut(o.index)&&i(e))t=t[o.index%t.length];else if(W(t[0])){const l=t,c=s._scopes.filter(u=>u!==l);t=[];for(const u of l){const f=Js(c,s,e,u);t.push(Je(f,o,r&&r[e],a))}}return t}function al(e,t,n){return we(e)?e(t,n):e}const _h=(e,t)=>e===!0?t:typeof e=="string"?Ze(t,e):void 0;function bh(e,t,n,i,s){for(const o of t){const r=_h(n,o);if(r){e.add(r);const a=al(r._fallback,n,s);if(Ut(a)&&a!==n&&a!==i)return a}else if(r===!1&&Ut(i)&&n!==i)return null}return!1}function Js(e,t,n,i){const s=t._rootScopes,o=al(t._fallback,n,i),r=[...e,...s],a=new Set;a.add(i);let l=ir(a,r,n,o||n,i);return l===null||Ut(o)&&o!==n&&(l=ir(a,r,o,l,i),l===null)?!1:Qs(Array.from(a),[""],s,o,()=>yh(t,n,i))}function ir(e,t,n,i,s){for(;n;)n=bh(e,t,n,i,s);return n}function yh(e,t,n){const i=e._getTarget();t in i||(i[t]={});const s=i[t];return ot(s)&&W(n)?n:s||{}}function vh(e,t,n,i){let s;for(const o of t)if(s=ll(hh(o,e),n),Ut(s))return Zs(e,s)?Js(n,i,e,s):s}function ll(e,t){for(const n of t){if(!n)continue;const i=n[e];if(Ut(i))return i}}function sr(e){let t=e._keys;return t||(t=e._keys=xh(e._scopes)),t}function xh(e){const t=new Set;for(const n of e)for(const i of Object.keys(n).filter(s=>!s.startsWith("_")))t.add(i);return Array.from(t)}function wh(e,t,n,i){const{iScale:s}=e,{key:o="r"}=this._parsing,r=new Array(i);let a,l,c,u;for(a=0,l=i;a<l;++a)c=a+n,u=t[c],r[a]={r:s.parse(Ze(u,o),c)};return r}function cl(){return typeof window<"u"&&typeof document<"u"}function to(e){let t=e.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function wi(e,t,n){let i;return typeof e=="string"?(i=parseInt(e,10),e.indexOf("%")!==-1&&(i=i/100*t.parentNode[n])):i=e,i}const Li=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function Sh(e,t){return Li(e).getPropertyValue(t)}const Ch=["top","right","bottom","left"];function De(e,t,n){const i={};n=n?"-"+n:"";for(let s=0;s<4;s++){const o=Ch[s];i[o]=parseFloat(e[t+"-"+o+n])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const Mh=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function kh(e,t){const n=e.touches,i=n&&n.length?n[0]:e,{offsetX:s,offsetY:o}=i;let r=!1,a,l;if(Mh(s,o,e.target))a=s,l=o;else{const c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function Ae(e,t){if("native"in e)return e;const{canvas:n,currentDevicePixelRatio:i}=t,s=Li(n),o=s.boxSizing==="border-box",r=De(s,"padding"),a=De(s,"border","width"),{x:l,y:c,box:u}=kh(e,n),f=r.left+(u&&a.left),d=r.top+(u&&a.top);let{width:h,height:g}=t;return o&&(h-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-f)/h*n.width/i),y:Math.round((c-d)/g*n.height/i)}}function Rh(e,t,n){let i,s;if(t===void 0||n===void 0){const o=to(e);if(!o)t=e.clientWidth,n=e.clientHeight;else{const r=o.getBoundingClientRect(),a=Li(o),l=De(a,"border","width"),c=De(a,"padding");t=r.width-c.width-l.width,n=r.height-c.height-l.height,i=wi(a.maxWidth,o,"clientWidth"),s=wi(a.maxHeight,o,"clientHeight")}}return{width:t,height:n,maxWidth:i||_i,maxHeight:s||_i}}const Nn=e=>Math.round(e*10)/10;function $h(e,t,n,i){const s=Li(e),o=De(s,"margin"),r=wi(s.maxWidth,e,"clientWidth")||_i,a=wi(s.maxHeight,e,"clientHeight")||_i,l=Rh(e,t,n);let{width:c,height:u}=l;if(s.boxSizing==="content-box"){const d=De(s,"border","width"),h=De(s,"padding");c-=h.width+d.width,u-=h.height+d.height}return c=Math.max(0,c-o.width),u=Math.max(0,i?c/i:u-o.height),c=Nn(Math.min(c,r,l.maxWidth)),u=Nn(Math.min(u,a,l.maxHeight)),c&&!u&&(u=Nn(c/2)),(t!==void 0||n!==void 0)&&i&&l.height&&u>l.height&&(u=l.height,c=Nn(Math.floor(u*i))),{width:c,height:u}}function or(e,t,n){const i=t||1,s=Math.floor(e.height*i),o=Math.floor(e.width*i);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const r=e.canvas;return r.style&&(n||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==i||r.height!==s||r.width!==o?(e.currentDevicePixelRatio=i,r.height=s,r.width=o,e.ctx.setTransform(i,0,0,i,0,0),!0):!1}const Ah=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch{}return e}();function rr(e,t){const n=Sh(e,t),i=n&&n.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}const Ph=function(e,t){return{x(n){return e+e+t-n},setWidth(n){t=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,i){return n-i},leftForLtr(n,i){return n-i}}},Fh=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function ts(e,t,n){return e?Ph(t,n):Fh()}function Dh(e,t){let n,i;(t==="ltr"||t==="rtl")&&(n=e.canvas.style,i=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=i)}function Oh(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}/*!
 * Chart.js v4.2.1
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */class Eh{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,n,i,s){const o=n.listeners[s],r=n.duration;o.forEach(a=>a({chart:t,initial:n.initial,numSteps:r,currentStep:Math.min(i-n.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Qa.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let n=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const o=i.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),o.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),n+=o.length}),this._lastDate=t,n===0&&(this._running=!1)}_getAnims(t){const n=this._charts;let i=n.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(t,i)),i}listen(t,n,i){this._getAnims(t).listeners[n].push(i)}add(t,n){!n||!n.length||this._getAnims(t).items.push(...n)}has(t){return this._getAnims(t).items.length>0}start(t){const n=this._charts.get(t);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;const n=this._charts.get(t);return!(!n||!n.running||!n.items.length)}stop(t){const n=this._charts.get(t);if(!n||!n.items.length)return;const i=n.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();n.items=[],this._notify(t,n,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var se=new Eh;const ar="transparent",Lh={boolean(e,t,n){return n>.5?t:e},color(e,t,n){const i=Jo(e||ar),s=i.valid&&Jo(t||ar);return s&&s.valid?s.mix(i,n).hexString():t},number(e,t,n){return e+(t-e)*n}};class Th{constructor(t,n,i,s){const o=n[i];s=Hn([t.to,s,o,t.from]);const r=Hn([t.from,o,s]);this._active=!0,this._fn=t.fn||Lh[t.type||typeof r],this._easing=_n[t.easing]||_n.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=n,this._prop=i,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(t,n,i){if(this._active){this._notify(!1);const s=this._target[this._prop],o=i-this._start,r=this._duration-o;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Hn([t.to,n,s,t.from]),this._from=Hn([t.from,s,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const n=t-this._start,i=this._duration,s=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||n<i),!this._active){this._target[s]=a,this._notify(!0);return}if(n<0){this._target[s]=o;return}l=n/i%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((n,i)=>{t.push({res:n,rej:i})})}_notify(t){const n=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][n]()}}class ul{constructor(t,n){this._chart=t,this._properties=new Map,this.configure(n)}configure(t){if(!W(t))return;const n=Object.keys(ut.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{const o=t[s];if(!W(o))return;const r={};for(const a of n)r[a]=o[a];(ot(o.properties)&&o.properties||[s]).forEach(a=>{(a===s||!i.has(a))&&i.set(a,r)})})}_animateOptions(t,n){const i=n.options,s=Vh(t,i);if(!s)return[];const o=this._createAnimations(s,i);return i.$shared&&Ih(t.options.$animations,i).then(()=>{t.options=i},()=>{}),o}_createAnimations(t,n){const i=this._properties,s=[],o=t.$animations||(t.$animations={}),r=Object.keys(n),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,n));continue}const u=n[c];let f=o[c];const d=i.get(c);if(f)if(d&&f.active()){f.update(d,u,a);continue}else f.cancel();if(!d||!d.duration){t[c]=u;continue}o[c]=f=new Th(d,t,c,u),s.push(f)}return s}update(t,n){if(this._properties.size===0){Object.assign(t,n);return}const i=this._createAnimations(t,n);if(i.length)return se.add(this._chart,i),!0}}function Ih(e,t){const n=[],i=Object.keys(t);for(let s=0;s<i.length;s++){const o=e[i[s]];o&&o.active()&&n.push(o.wait())}return Promise.all(n)}function Vh(e,t){if(!t)return;let n=e.options;if(!n){e.options=t;return}return n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function lr(e,t){const n=e&&e.options||{},i=n.reverse,s=n.min===void 0?t:0,o=n.max===void 0?t:0;return{start:i?o:s,end:i?s:o}}function zh(e,t,n){if(n===!1)return!1;const i=lr(e,n),s=lr(t,n);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function Hh(e){let t,n,i,s;return W(e)?(t=e.top,n=e.right,i=e.bottom,s=e.left):t=n=i=s=e,{top:t,right:n,bottom:i,left:s,disabled:e===!1}}function fl(e,t){const n=[],i=e._getSortedDatasetMetas(t);let s,o;for(s=0,o=i.length;s<o;++s)n.push(i[s].index);return n}function cr(e,t,n,i={}){const s=e.keys,o=i.mode==="single";let r,a,l,c;if(t!==null){for(r=0,a=s.length;r<a;++r){if(l=+s[r],l===n){if(i.all)continue;break}c=e.values[l],dt(c)&&(o||t===0||bi(t)===bi(c))&&(t+=c)}return t}}function Nh(e){const t=Object.keys(e),n=new Array(t.length);let i,s,o;for(i=0,s=t.length;i<s;++i)o=t[i],n[i]={x:o,y:e[o]};return n}function ur(e,t){const n=e&&e.options.stacked;return n||n===void 0&&t.stack!==void 0}function Bh(e,t,n){return`${e.id}.${t.id}.${n.stack||n.type}`}function jh(e){const{min:t,max:n,minDefined:i,maxDefined:s}=e.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:s?n:Number.POSITIVE_INFINITY}}function Wh(e,t,n){const i=e[t]||(e[t]={});return i[n]||(i[n]={})}function fr(e,t,n,i){for(const s of t.getMatchingVisibleMetas(i).reverse()){const o=e[s.index];if(n&&o>0||!n&&o<0)return s.index}return null}function dr(e,t){const{chart:n,_cachedMeta:i}=e,s=n._stacks||(n._stacks={}),{iScale:o,vScale:r,index:a}=i,l=o.axis,c=r.axis,u=Bh(o,r,i),f=t.length;let d;for(let h=0;h<f;++h){const g=t[h],{[l]:p,[c]:m}=g,_=g._stacks||(g._stacks={});d=_[c]=Wh(s,u,p),d[a]=m,d._top=fr(d,r,!0,i.type),d._bottom=fr(d,r,!1,i.type);const b=d._visualValues||(d._visualValues={});b[a]=m}}function es(e,t){const n=e.scales;return Object.keys(n).filter(i=>n[i].axis===t).shift()}function Gh(e,t){return Ie(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function qh(e,t,n){return Ie(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:"default",type:"data"})}function on(e,t){const n=e.controller.index,i=e.vScale&&e.vScale.axis;if(i){t=t||e._parsed;for(const s of t){const o=s._stacks;if(!o||o[i]===void 0||o[i][n]===void 0)return;delete o[i][n],o[i]._visualValues!==void 0&&o[i]._visualValues[n]!==void 0&&delete o[i]._visualValues[n]}}}const ns=e=>e==="reset"||e==="none",hr=(e,t)=>t?e:Object.assign({},e),Yh=(e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:fl(n,!0),values:null};class Ue{constructor(t,n){this.chart=t,this._ctx=t.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=ur(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&on(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,n=this._cachedMeta,i=this.getDataset(),s=(f,d,h,g)=>f==="x"?d:f==="r"?g:h,o=n.xAxisID=et(i.xAxisID,es(t,"x")),r=n.yAxisID=et(i.yAxisID,es(t,"y")),a=n.rAxisID=et(i.rAxisID,es(t,"r")),l=n.indexAxis,c=n.iAxisID=s(l,o,r,a),u=n.vAxisID=s(l,r,o,a);n.xScale=this.getScaleForId(o),n.yScale=this.getScaleForId(r),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(c),n.vScale=this.getScaleForId(u)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const n=this._cachedMeta;return t===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Ko(this._data,this),t._stacked&&on(t)}_dataCheck(){const t=this.getDataset(),n=t.data||(t.data=[]),i=this._data;if(W(n))this._data=Nh(n);else if(i!==n){if(i){Ko(i,this);const s=this._cachedMeta;on(s),s._parsed=[]}n&&Object.isExtensible(n)&&Wd(n,this),this._syncList=[],this._data=n}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const n=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const o=n._stacked;n._stacked=ur(n.vScale,n),n.stack!==i.stack&&(s=!0,on(n),n.stack=i.stack),this._resyncElements(t),(s||o!==n._stacked)&&dr(this,n._parsed)}configure(){const t=this.chart.config,n=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),n,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,n){const{_cachedMeta:i,_data:s}=this,{iScale:o,_stacked:r}=i,a=o.axis;let l=t===0&&n===s.length?!0:i._sorted,c=t>0&&i._parsed[t-1],u,f,d;if(this._parsing===!1)i._parsed=s,i._sorted=!0,d=s;else{ot(s[t])?d=this.parseArrayData(i,s,t,n):W(s[t])?d=this.parseObjectData(i,s,t,n):d=this.parsePrimitiveData(i,s,t,n);const h=()=>f[a]===null||c&&f[a]<c[a];for(u=0;u<n;++u)i._parsed[u+t]=f=d[u],l&&(h()&&(l=!1),c=f);i._sorted=l}r&&dr(this,d)}parsePrimitiveData(t,n,i,s){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),u=o===r,f=new Array(s);let d,h,g;for(d=0,h=s;d<h;++d)g=d+i,f[d]={[a]:u||o.parse(c[g],g),[l]:r.parse(n[g],g)};return f}parseArrayData(t,n,i,s){const{xScale:o,yScale:r}=t,a=new Array(s);let l,c,u,f;for(l=0,c=s;l<c;++l)u=l+i,f=n[u],a[l]={x:o.parse(f[0],u),y:r.parse(f[1],u)};return a}parseObjectData(t,n,i,s){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s);let u,f,d,h;for(u=0,f=s;u<f;++u)d=u+i,h=n[d],c[u]={x:o.parse(Ze(h,a),d),y:r.parse(Ze(h,l),d)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,n,i){const s=this.chart,o=this._cachedMeta,r=n[t.axis],a={keys:fl(s,!0),values:n._stacks[t.axis]._visualValues};return cr(a,r,o.index,{mode:i})}updateRangeFromParsed(t,n,i,s){const o=i[n.axis];let r=o===null?NaN:o;const a=s&&i._stacks[n.axis];s&&a&&(s.values=a,r=cr(s,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,n){const i=this._cachedMeta,s=i._parsed,o=i._sorted&&t===i.iScale,r=s.length,a=this._getOtherScale(t),l=Yh(n,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=jh(a);let d,h;function g(){h=s[d];const p=h[a.axis];return!dt(h[t.axis])||u>p||f<p}for(d=0;d<r&&!(!g()&&(this.updateRangeFromParsed(c,t,h,l),o));++d);if(o){for(d=r-1;d>=0;--d)if(!g()){this.updateRangeFromParsed(c,t,h,l);break}}return c}getAllParsedValues(t){const n=this._cachedMeta._parsed,i=[];let s,o,r;for(s=0,o=n.length;s<o;++s)r=n[s][t.axis],dt(r)&&i.push(r);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const n=this._cachedMeta,i=n.iScale,s=n.vScale,o=this.getParsed(t);return{label:i?""+i.getLabelForValue(o[i.axis]):"",value:s?""+s.getLabelForValue(o[s.axis]):""}}_update(t){const n=this._cachedMeta;this.update(t||"default"),n._clip=Hh(et(this.options.clip,zh(n.xScale,n.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this.chart,i=this._cachedMeta,s=i.data||[],o=n.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop;let u;for(i.dataset&&i.dataset.draw(t,o,a,l),u=a;u<a+l;++u){const f=s[u];f.hidden||(f.active&&c?r.push(f):f.draw(t,o))}for(u=0;u<r.length;++u)r[u].draw(t,o)}getStyle(t,n){const i=n?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,n,i){const s=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=qh(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=s.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Gh(this.chart.getContext(),this.index)),o.dataset=s,o.index=o.datasetIndex=this.index;return o.active=!!n,o.mode=i,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,n){return this._resolveElementOptions(this.dataElementType.id,n,t)}_resolveElementOptions(t,n="default",i){const s=n==="active",o=this._cachedDataOpts,r=t+"-"+n,a=o[r],l=this.enableOptionSharing&&Ut(i);if(a)return hr(a,l);const c=this.chart.config,u=c.datasetElementScopeKeys(this._type,t),f=s?[`${t}Hover`,"hover",t,""]:[t,""],d=c.getOptionScopes(this.getDataset(),u),h=Object.keys(ut.elements[t]),g=()=>this.getContext(i,s,n),p=c.resolveNamedOptions(d,h,g,f);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(hr(p,l))),p}_resolveAnimations(t,n,i){const s=this.chart,o=this._cachedDataOpts,r=`animation-${n}`,a=o[r];if(a)return a;let l;if(s.options.animation!==!1){const u=this.chart.config,f=u.datasetAnimationScopeKeys(this._type,n),d=u.getOptionScopes(this.getDataset(),f);l=u.createResolver(d,this.getContext(t,i,n))}const c=new ul(s,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,n){return!n||ns(t)||this.chart._animationsDisabled}_getSharedOptions(t,n){const i=this.resolveDataElementOptions(t,n),s=this._sharedOptions,o=this.getSharedOptions(i),r=this.includeOptions(n,o)||o!==s;return this.updateSharedOptions(o,n,i),{sharedOptions:o,includeOptions:r}}updateElement(t,n,i,s){ns(s)?Object.assign(t,i):this._resolveAnimations(n,s).update(t,i)}updateSharedOptions(t,n,i){t&&!ns(n)&&this._resolveAnimations(void 0,n).update(t,i)}_setStyle(t,n,i,s){t.active=s;const o=this.getStyle(n,s);this._resolveAnimations(n,i,s).update(t,{options:!s&&this.getSharedOptions(o)||o})}removeHoverStyle(t,n,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,n,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const n=this._data,i=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const s=i.length,o=n.length,r=Math.min(o,s);r&&this.parse(0,r),o>s?this._insertElements(s,o-s,t):o<s&&this._removeElements(o,s-o)}_insertElements(t,n,i=!0){const s=this._cachedMeta,o=s.data,r=t+n;let a;const l=c=>{for(c.length+=n,a=c.length-1;a>=r;a--)c[a]=c[a-n]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(t,n),i&&this.updateElements(o,t,n,"reset")}updateElements(t,n,i,s){}_removeElements(t,n){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,n);i._stacked&&on(i,s)}i.data.splice(t,n)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[n,i,s]=t;this[n](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,n){n&&this._sync(["_removeElements",t,n]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}H(Ue,"defaults",{}),H(Ue,"datasetElementType",null),H(Ue,"dataElementType",null);function Uh(e,t,n){let i=1,s=1,o=0,r=0;if(t<at){const a=e,l=a+t,c=Math.cos(a),u=Math.sin(a),f=Math.cos(l),d=Math.sin(l),h=(y,x,v)=>vi(y,a,l,!0)?1:Math.max(x,x*n,v,v*n),g=(y,x,v)=>vi(y,a,l,!0)?-1:Math.min(x,x*n,v,v*n),p=h(0,c,f),m=h(lt,u,d),_=g(ct,c,f),b=g(ct+lt,u,d);i=(p-_)/2,s=(m-b)/2,o=-(p+_)/2,r=-(m+b)/2}return{ratioX:i,ratioY:s,offsetX:o,offsetY:r}}class We extends Ue{constructor(t,n){super(t,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,n){const i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let o=l=>+i[l];if(W(i[t])){const{key:l="value"}=this._parsing;o=c=>+Ze(i[c],l)}let r,a;for(r=t,a=t+n;r<a;++r)s._parsed[r]=o(r)}}_getRotation(){return jt(this.options.rotation-90)}_getCircumference(){return jt(this.options.circumference)}_getRotationExtents(){let t=at,n=-at;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,o=s._getRotation(),r=s._getCircumference();t=Math.min(t,o),n=Math.max(n,o+r)}return{rotation:t,circumference:n-t}}update(t){const n=this.chart,{chartArea:i}=n,s=this._cachedMeta,o=s.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-r)/2,0),l=Math.min(Pd(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:u,rotation:f}=this._getRotationExtents(),{ratioX:d,ratioY:h,offsetX:g,offsetY:p}=Uh(f,u,l),m=(i.width-r)/d,_=(i.height-r)/h,b=Math.max(Math.min(m,_)/2,0),y=qa(this.options.radius,b),x=Math.max(y*l,0),v=(y-x)/this._getVisibleDatasetWeightTotal();this.offsetX=g*y,this.offsetY=p*y,s.total=this.calculateTotal(),this.outerRadius=y-v*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-v*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,n){const i=this.options,s=this._cachedMeta,o=this._getCircumference();return n&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||s._parsed[t]===null||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*o/at)}updateElements(t,n,i,s){const o=s==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,u=(a.left+a.right)/2,f=(a.top+a.bottom)/2,d=o&&c.animateScale,h=d?0:this.innerRadius,g=d?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(n,s);let _=this._getRotation(),b;for(b=0;b<n;++b)_+=this._circumference(b,o);for(b=n;b<n+i;++b){const y=this._circumference(b,o),x=t[b],v={x:u+this.offsetX,y:f+this.offsetY,startAngle:_,endAngle:_+y,circumference:y,outerRadius:g,innerRadius:h};m&&(v.options=p||this.resolveDataElementOptions(b,x.active?"active":s)),_+=y,this.updateElement(x,b,v,s)}}calculateTotal(){const t=this._cachedMeta,n=t.data;let i=0,s;for(s=0;s<n.length;s++){const o=t._parsed[s];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(s)&&!n[s].hidden&&(i+=Math.abs(o))}return i}calculateCircumference(t){const n=this._cachedMeta.total;return n>0&&!isNaN(t)?at*(Math.abs(t)/n):0}getLabelAndValue(t){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=$n(n._parsed[t],i.options.locale);return{label:s[t]||"",value:o}}getMaxBorderWidth(t){let n=0;const i=this.chart;let s,o,r,a,l;if(!t){for(s=0,o=i.data.datasets.length;s<o;++s)if(i.isDatasetVisible(s)){r=i.getDatasetMeta(s),t=r.data,a=r.controller;break}}if(!t)return 0;for(s=0,o=t.length;s<o;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(t){let n=0;for(let i=0,s=t.length;i<s;++i){const o=this.resolveDataElementOptions(i);n=Math.max(n,o.offset||0,o.hoverOffset||0)}return n}_getRingWeightOffset(t){let n=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(n+=this._getRingWeight(i));return n}_getRingWeight(t){return Math.max(et(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}H(We,"id","doughnut"),H(We,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),H(We,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"}),H(We,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}}});class Jn extends Ue{constructor(t,n){super(t,n),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=$n(n._parsed[t].r,i.options.locale);return{label:s[t]||"",value:o}}parseObjectData(t,n,i,s){return wh.bind(this)(t,n,i,s)}update(t){const n=this._cachedMeta.data;this._updateRadius(),this.updateElements(n,0,n.length,t)}getMinMax(){const t=this._cachedMeta,n={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,s)=>{const o=this.getParsed(s).r;!isNaN(o)&&this.chart.getDataVisibility(s)&&(o<n.min&&(n.min=o),o>n.max&&(n.max=o))}),n}_updateRadius(){const t=this.chart,n=t.chartArea,i=t.options,s=Math.min(n.right-n.left,n.bottom-n.top),o=Math.max(s/2,0),r=Math.max(i.cutoutPercentage?o/100*i.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,n,i,s){const o=s==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,u=c.xCenter,f=c.yCenter,d=c.getIndexAngle(0)-.5*ct;let h=d,g;const p=360/this.countVisibleElements();for(g=0;g<n;++g)h+=this._computeAngle(g,s,p);for(g=n;g<n+i;g++){const m=t[g];let _=h,b=h+this._computeAngle(g,s,p),y=r.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;h=b,o&&(l.animateScale&&(y=0),l.animateRotate&&(_=b=d));const x={x:u,y:f,innerRadius:0,outerRadius:y,startAngle:_,endAngle:b,options:this.resolveDataElementOptions(g,m.active?"active":s)};this.updateElement(m,g,x,s)}}countVisibleElements(){const t=this._cachedMeta;let n=0;return t.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&n++}),n}_computeAngle(t,n,i){return this.chart.getDataVisibility(t)?jt(this.resolveDataElementOptions(t,n).angle||i):0}}H(Jn,"id","polarArea"),H(Jn,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),H(Jn,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class ks extends We{}H(ks,"id","pie"),H(ks,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});function Re(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class eo{static override(t){Object.assign(eo.prototype,t)}constructor(t){this.options=t||{}}init(){}formats(){return Re()}parse(){return Re()}format(){return Re()}add(){return Re()}diff(){return Re()}startOf(){return Re()}endOf(){return Re()}}var Xh={_date:eo};function Kh(e,t,n,i){const{controller:s,data:o,_sorted:r}=e,a=s._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const l=a._reversePixels?Bd:Ss;if(i){if(s._sharedOptions){const c=o[0],u=typeof c.getRange=="function"&&c.getRange(t);if(u){const f=l(o,t,n-u),d=l(o,t,n+u);return{lo:f.lo,hi:d.hi}}}}else return l(o,t,n)}return{lo:0,hi:o.length-1}}function An(e,t,n,i,s){const o=e.getSortedVisibleDatasetMetas(),r=n[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:u}=o[a],{lo:f,hi:d}=Kh(o[a],t,r,s);for(let h=f;h<=d;++h){const g=u[h];g.skip||i(g,c,h)}}}function Qh(e){const t=e.indexOf("x")!==-1,n=e.indexOf("y")!==-1;return function(i,s){const o=t?Math.abs(i.x-s.x):0,r=n?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function is(e,t,n,i,s){const o=[];return!s&&!e.isPointInArea(t)||An(e,n,t,function(a,l,c){!s&&!nl(a,e.chartArea,0)||a.inRange(t.x,t.y,i)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Zh(e,t,n,i){let s=[];function o(r,a,l){const{startAngle:c,endAngle:u}=r.getProps(["startAngle","endAngle"],i),{angle:f}=Xa(r,{x:t.x,y:t.y});vi(f,c,u)&&s.push({element:r,datasetIndex:a,index:l})}return An(e,n,t,o),s}function Jh(e,t,n,i,s,o){let r=[];const a=Qh(n);let l=Number.POSITIVE_INFINITY;function c(u,f,d){const h=u.inRange(t.x,t.y,s);if(i&&!h)return;const g=u.getCenterPoint(s);if(!(!!o||e.isPointInArea(g))&&!h)return;const m=a(t,g);m<l?(r=[{element:u,datasetIndex:f,index:d}],l=m):m===l&&r.push({element:u,datasetIndex:f,index:d})}return An(e,n,t,c),r}function ss(e,t,n,i,s,o){return!o&&!e.isPointInArea(t)?[]:n==="r"&&!i?Zh(e,t,n,s):Jh(e,t,n,i,s,o)}function gr(e,t,n,i,s){const o=[],r=n==="x"?"inXRange":"inYRange";let a=!1;return An(e,n,t,(l,c,u)=>{l[r](t[n],s)&&(o.push({element:l,datasetIndex:c,index:u}),a=a||l.inRange(t.x,t.y,s))}),i&&!a?[]:o}var tg={evaluateInteractionItems:An,modes:{index(e,t,n,i){const s=Ae(t,e),o=n.axis||"x",r=n.includeInvisible||!1,a=n.intersect?is(e,s,o,i,r):ss(e,s,o,!1,i,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const u=a[0].index,f=c.data[u];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:u})}),l):[]},dataset(e,t,n,i){const s=Ae(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;let a=n.intersect?is(e,s,o,i,r):ss(e,s,o,!1,i,r);if(a.length>0){const l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let u=0;u<c.length;++u)a.push({element:c[u],datasetIndex:l,index:u})}return a},point(e,t,n,i){const s=Ae(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return is(e,s,o,i,r)},nearest(e,t,n,i){const s=Ae(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return ss(e,s,o,n.intersect,i,r)},x(e,t,n,i){const s=Ae(t,e);return gr(e,s,"x",n.intersect,i)},y(e,t,n,i){const s=Ae(t,e);return gr(e,s,"y",n.intersect,i)}}};const dl=["left","top","right","bottom"];function rn(e,t){return e.filter(n=>n.pos===t)}function pr(e,t){return e.filter(n=>dl.indexOf(n.pos)===-1&&n.box.axis===t)}function an(e,t){return e.sort((n,i)=>{const s=t?i:n,o=t?n:i;return s.weight===o.weight?s.index-o.index:s.weight-o.weight})}function eg(e){const t=[];let n,i,s,o,r,a;for(n=0,i=(e||[]).length;n<i;++n)s=e[n],{position:o,options:{stack:r,stackWeight:a=1}}=s,t.push({index:n,box:s,pos:o,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&o+r,stackWeight:a});return t}function ng(e){const t={};for(const n of e){const{stack:i,pos:s,stackWeight:o}=n;if(!i||!dl.includes(s))continue;const r=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function ig(e,t){const n=ng(e),{vBoxMaxWidth:i,hBoxMaxHeight:s}=t;let o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];const{fullSize:l}=a.box,c=n[a.stack],u=c&&a.stackWeight/c.weight;a.horizontal?(a.width=u?u*i:l&&t.availableWidth,a.height=s):(a.width=i,a.height=u?u*s:l&&t.availableHeight)}return n}function sg(e){const t=eg(e),n=an(t.filter(c=>c.box.fullSize),!0),i=an(rn(t,"left"),!0),s=an(rn(t,"right")),o=an(rn(t,"top"),!0),r=an(rn(t,"bottom")),a=pr(t,"x"),l=pr(t,"y");return{fullSize:n,leftAndTop:i.concat(o),rightAndBottom:s.concat(l).concat(r).concat(a),chartArea:rn(t,"chartArea"),vertical:i.concat(s).concat(l),horizontal:o.concat(r).concat(a)}}function mr(e,t,n,i){return Math.max(e[n],t[n])+Math.max(e[i],t[i])}function hl(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function og(e,t,n,i){const{pos:s,box:o}=n,r=e.maxPadding;if(!W(s)){n.size&&(e[s]-=n.size);const f=i[n.stack]||{size:0,count:1};f.size=Math.max(f.size,n.horizontal?o.height:o.width),n.size=f.size/f.count,e[s]+=n.size}o.getPadding&&hl(r,o.getPadding());const a=Math.max(0,t.outerWidth-mr(r,e,"left","right")),l=Math.max(0,t.outerHeight-mr(r,e,"top","bottom")),c=a!==e.w,u=l!==e.h;return e.w=a,e.h=l,n.horizontal?{same:c,other:u}:{same:u,other:c}}function rg(e){const t=e.maxPadding;function n(i){const s=Math.max(t[i]-e[i],0);return e[i]+=s,s}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}function ag(e,t){const n=t.maxPadding;function i(s){const o={left:0,top:0,right:0,bottom:0};return s.forEach(r=>{o[r]=Math.max(t[r],n[r])}),o}return i(e?["left","right"]:["top","bottom"])}function fn(e,t,n,i){const s=[];let o,r,a,l,c,u;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||t.w,a.height||t.h,ag(a.horizontal,t));const{same:f,other:d}=og(t,n,a,i);c|=f&&s.length,u=u||d,l.fullSize||s.push(a)}return c&&fn(s,t,n,i)||u}function Bn(e,t,n,i,s){e.top=n,e.left=t,e.right=t+i,e.bottom=n+s,e.width=i,e.height=s}function _r(e,t,n,i){const s=n.padding;let{x:o,y:r}=t;for(const a of e){const l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},u=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*u,d=c.size||l.height;Ut(c.start)&&(r=c.start),l.fullSize?Bn(l,s.left,r,n.outerWidth-s.right-s.left,d):Bn(l,t.left+c.placed,r,f,d),c.start=r,c.placed+=f,r=l.bottom}else{const f=t.h*u,d=c.size||l.width;Ut(c.start)&&(o=c.start),l.fullSize?Bn(l,o,s.top,d,n.outerHeight-s.bottom-s.top):Bn(l,o,t.top+c.placed,d,f),c.start=o,c.placed+=f,o=l.right}}t.x=o,t.y=r}var me={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(n){t.draw(n)}}]},e.boxes.push(t)},removeBox(e,t){const n=e.boxes?e.boxes.indexOf(t):-1;n!==-1&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,i){if(!e)return;const s=$t(e.options.layout.padding),o=Math.max(t-s.width,0),r=Math.max(n-s.height,0),a=sg(e.boxes),l=a.vertical,c=a.horizontal;G(e.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const u=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:n,padding:s,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/u,hBoxMaxHeight:r/2}),d=Object.assign({},s);hl(d,$t(i));const h=Object.assign({maxPadding:d,w:o,h:r,x:s.left,y:s.top},s),g=ig(l.concat(c),f);fn(a.fullSize,h,f,g),fn(l,h,f,g),fn(c,h,f,g)&&fn(l,h,f,g),rg(h),_r(a.leftAndTop,h,f,g),h.x+=h.w,h.y+=h.h,_r(a.rightAndBottom,h,f,g),e.chartArea={left:h.left,top:h.top,right:h.left+h.w,bottom:h.top+h.h,height:h.h,width:h.w},G(a.chartArea,p=>{const m=p.box;Object.assign(m,e.chartArea),m.update(h.w,h.h,{left:0,top:0,right:0,bottom:0})})}};class gl{acquireContext(t,n){}releaseContext(t){return!1}addEventListener(t,n,i){}removeEventListener(t,n,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,n,i,s){return n=Math.max(0,n||t.width),i=i||t.height,{width:n,height:Math.max(0,s?Math.floor(n/s):i)}}isAttached(t){return!0}updateConfig(t){}}class lg extends gl{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const ti="$chartjs",cg={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},br=e=>e===null||e==="";function ug(e,t){const n=e.style,i=e.getAttribute("height"),s=e.getAttribute("width");if(e[ti]={initial:{height:i,width:s,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",br(s)){const o=rr(e,"width");o!==void 0&&(e.width=o)}if(br(i))if(e.style.height==="")e.height=e.width/(t||2);else{const o=rr(e,"height");o!==void 0&&(e.height=o)}return e}const pl=Ah?{passive:!0}:!1;function fg(e,t,n){e.addEventListener(t,n,pl)}function dg(e,t,n){e.canvas.removeEventListener(t,n,pl)}function hg(e,t){const n=cg[e.type]||e.type,{x:i,y:s}=Ae(e,t);return{type:n,chart:t,native:e,x:i!==void 0?i:null,y:s!==void 0?s:null}}function Si(e,t){for(const n of e)if(n===t||n.contains(t))return!0}function gg(e,t,n){const i=e.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Si(a.addedNodes,i),r=r&&!Si(a.removedNodes,i);r&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}function pg(e,t,n){const i=e.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Si(a.removedNodes,i),r=r&&!Si(a.addedNodes,i);r&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}const Cn=new Map;let yr=0;function ml(){const e=window.devicePixelRatio;e!==yr&&(yr=e,Cn.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function mg(e,t){Cn.size||window.addEventListener("resize",ml),Cn.set(e,t)}function _g(e){Cn.delete(e),Cn.size||window.removeEventListener("resize",ml)}function bg(e,t,n){const i=e.canvas,s=i&&to(i);if(!s)return;const o=Za((a,l)=>{const c=s.clientWidth;n(a,l),c<s.clientWidth&&n()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,u=l.contentRect.height;c===0&&u===0||o(c,u)});return r.observe(s),mg(e,o),r}function os(e,t,n){n&&n.disconnect(),t==="resize"&&_g(e)}function yg(e,t,n){const i=e.canvas,s=Za(o=>{e.ctx!==null&&n(hg(o,e))},e);return fg(i,t,s),s}class vg extends gl{acquireContext(t,n){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(ug(t,n),i):null}releaseContext(t){const n=t.canvas;if(!n[ti])return!1;const i=n[ti].initial;["height","width"].forEach(o=>{const r=i[o];tt(r)?n.removeAttribute(o):n.setAttribute(o,r)});const s=i.style||{};return Object.keys(s).forEach(o=>{n.style[o]=s[o]}),n.width=n.width,delete n[ti],!0}addEventListener(t,n,i){this.removeEventListener(t,n);const s=t.$proxies||(t.$proxies={}),r={attach:gg,detach:pg,resize:bg}[n]||yg;s[n]=r(t,n,i)}removeEventListener(t,n){const i=t.$proxies||(t.$proxies={}),s=i[n];if(!s)return;({attach:os,detach:os,resize:os}[n]||dg)(t,n,s),i[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,n,i,s){return $h(t,n,i,s)}isAttached(t){const n=to(t);return!!(n&&n.isConnected)}}function xg(e){return!cl()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?lg:vg}var Xn;let Pn=(Xn=class{constructor(){H(this,"active",!1)}tooltipPosition(t){const{x:n,y:i}=this.getProps(["x","y"],t);return{x:n,y:i}}hasValue(){return yi(this.x)&&yi(this.y)}getProps(t,n){const i=this.$animations;if(!n||!i)return this;const s={};return t.forEach(o=>{s[o]=i[o]&&i[o].active()?i[o]._to:this[o]}),s}},H(Xn,"defaults",{}),H(Xn,"defaultRoutes"),Xn);function wg(e,t){const n=e.options.ticks,i=Sg(e),s=Math.min(n.maxTicksLimit||i,i),o=n.major.enabled?Mg(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>s)return kg(t,c,o,r/s),c;const u=Cg(o,t,s);if(r>0){let f,d;const h=r>1?Math.round((l-a)/(r-1)):null;for(jn(t,c,u,tt(h)?0:a-h,a),f=0,d=r-1;f<d;f++)jn(t,c,u,o[f],o[f+1]);return jn(t,c,u,l,tt(h)?t.length:l+h),c}return jn(t,c,u),c}function Sg(e){const t=e.options.offset,n=e._tickSize(),i=e._length/n+(t?0:1),s=e._maxLength/n;return Math.floor(Math.min(i,s))}function Cg(e,t,n){const i=Rg(e),s=t.length/n;if(!i)return Math.max(s,1);const o=Id(i);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>s)return l}return Math.max(s,1)}function Mg(e){const t=[];let n,i;for(n=0,i=e.length;n<i;n++)e[n].major&&t.push(n);return t}function kg(e,t,n,i){let s=0,o=n[0],r;for(i=Math.ceil(i),r=0;r<e.length;r++)r===o&&(t.push(e[r]),s++,o=n[s*i])}function jn(e,t,n,i,s){const o=et(i,0),r=Math.min(et(s,e.length),e.length);let a=0,l,c,u;for(n=Math.ceil(n),s&&(l=s-i,n=l/Math.floor(l/n)),u=o;u<0;)a++,u=Math.round(o+a*n);for(c=Math.max(o,0);c<r;c++)c===u&&(t.push(e[c]),a++,u=Math.round(o+a*n))}function Rg(e){const t=e.length;let n,i;if(t<2)return!1;for(i=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==i)return!1;return i}const $g=e=>e==="left"?"right":e==="right"?"left":e,vr=(e,t,n)=>t==="top"||t==="left"?e[t]+n:e[t]-n,xr=(e,t)=>Math.min(t||e,e);function wr(e,t){const n=[],i=e.length/t,s=e.length;let o=0;for(;o<s;o+=i)n.push(e[Math.floor(o)]);return n}function Ag(e,t,n){const i=e.ticks.length,s=Math.min(t,i-1),o=e._startPixel,r=e._endPixel,a=1e-6;let l=e.getPixelForTick(s),c;if(!(n&&(i===1?c=Math.max(l-o,r-l):t===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(s-1))/2,l+=s<t?c:-c,l<o-a||l>r+a)))return l}function Pg(e,t){G(e,n=>{const i=n.gc,s=i.length/2;let o;if(s>t){for(o=0;o<s;++o)delete n.data[i[o]];i.splice(0,s)}})}function ln(e){return e.drawTicks?e.tickLength:0}function Sr(e,t){if(!e.display)return 0;const n=yt(e.font,t),i=$t(e.padding);return(ot(e.text)?e.text.length:1)*n.lineHeight+i.height}function Fg(e,t){return Ie(e,{scale:t,type:"scale"})}function Dg(e,t,n){return Ie(e,{tick:n,index:t,type:"tick"})}function Og(e,t,n){let i=Ja(e);return(n&&t!=="right"||!n&&t==="right")&&(i=$g(i)),i}function Eg(e,t,n,i){const{top:s,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:u}=l;let f=0,d,h,g;const p=r-s,m=a-o;if(e.isHorizontal()){if(h=mn(i,o,a),W(n)){const _=Object.keys(n)[0],b=n[_];g=u[_].getPixelForValue(b)+p-t}else n==="center"?g=(c.bottom+c.top)/2+p-t:g=vr(e,n,t);d=a-o}else{if(W(n)){const _=Object.keys(n)[0],b=n[_];h=u[_].getPixelForValue(b)-m+t}else n==="center"?h=(c.left+c.right)/2-m+t:h=vr(e,n,t);g=mn(i,r,s),f=n==="left"?-lt:lt}return{titleX:h,titleY:g,maxWidth:d,rotation:f}}class Ve extends Pn{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,n){return t}getUserBounds(){let{_userMin:t,_userMax:n,_suggestedMin:i,_suggestedMax:s}=this;return t=St(t,Number.POSITIVE_INFINITY),n=St(n,Number.NEGATIVE_INFINITY),i=St(i,Number.POSITIVE_INFINITY),s=St(s,Number.NEGATIVE_INFINITY),{min:St(t,i),max:St(n,s),minDefined:dt(t),maxDefined:dt(n)}}getMinMax(t){let{min:n,max:i,minDefined:s,maxDefined:o}=this.getUserBounds(),r;if(s&&o)return{min:n,max:i};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),s||(n=Math.min(n,r.min)),o||(i=Math.max(i,r.max));return n=o&&n>i?i:n,i=s&&n>i?n:i,{min:St(n,St(i,n)),max:St(i,St(n,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){rt(this.options.beforeUpdate,[this])}update(t,n,i){const{beginAtZero:s,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=n,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=dh(this,o,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?wr(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=wg(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,n,i;this.isHorizontal()?(n=this.left,i=this.right):(n=this.top,i=this.bottom,t=!t),this._startPixel=n,this._endPixel=i,this._reversePixels=t,this._length=i-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){rt(this.options.afterUpdate,[this])}beforeSetDimensions(){rt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){rt(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),rt(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){rt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const n=this.options.ticks;let i,s,o;for(i=0,s=t.length;i<s;i++)o=t[i],o.label=rt(n.callback,[o.value,i,t],this)}afterTickToLabelConversion(){rt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){rt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,n=t.ticks,i=xr(this.ticks.length,t.ticks.maxTicksLimit),s=n.minRotation||0,o=n.maxRotation;let r=s,a,l,c;if(!this._isVisible()||!n.display||s>=o||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const u=this._getLabelSizes(),f=u.widest.width,d=u.highest.height,h=Mt(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/i:h/(i-1),f+6>a&&(a=h/(i-(t.offset?.5:1)),l=this.maxHeight-ln(t.grid)-n.padding-Sr(t.title,this.chart.options.font),c=Math.sqrt(f*f+d*d),r=Us(Math.min(Math.asin(Mt((u.highest.height+6)/a,-1,1)),Math.asin(Mt(l/c,-1,1))-Math.asin(Mt(d/c,-1,1)))),r=Math.max(s,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){rt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){rt(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:n,options:{ticks:i,title:s,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Sr(s,n.options.font);if(a?(t.width=this.maxWidth,t.height=ln(o)+l):(t.height=this.maxHeight,t.width=ln(o)+l),i.display&&this.ticks.length){const{first:c,last:u,widest:f,highest:d}=this._getLabelSizes(),h=i.padding*2,g=jt(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const _=i.mirror?0:m*f.width+p*d.height;t.height=Math.min(this.maxHeight,t.height+_+h)}else{const _=i.mirror?0:p*f.width+m*d.height;t.width=Math.min(this.maxWidth,t.width+_+h)}this._calculatePadding(c,u,m,p)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,n,i,s){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const u=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let d=0,h=0;l?c?(d=s*t.width,h=i*n.height):(d=i*t.height,h=s*n.width):o==="start"?h=n.width:o==="end"?d=t.width:o!=="inner"&&(d=t.width/2,h=n.width/2),this.paddingLeft=Math.max((d-u+r)*this.width/(this.width-u),0),this.paddingRight=Math.max((h-f+r)*this.width/(this.width-f),0)}else{let u=n.height/2,f=t.height/2;o==="start"?(u=0,f=t.height):o==="end"&&(u=n.height,f=0),this.paddingTop=u+r,this.paddingBottom=f+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){rt(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:n}=this.options;return n==="top"||n==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let n,i;for(n=0,i=t.length;n<i;n++)tt(t[n].label)&&(t.splice(n,1),i--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const n=this.options.ticks.sampleSize;let i=this.ticks;n<i.length&&(i=wr(i,n)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,n,i){const{ctx:s,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(n/xr(n,i));let c=0,u=0,f,d,h,g,p,m,_,b,y,x,v;for(f=0;f<n;f+=l){if(g=t[f].label,p=this._resolveTickFontOptions(f),s.font=m=p.string,_=o[m]=o[m]||{data:{},gc:[]},b=p.lineHeight,y=x=0,!tt(g)&&!ot(g))y=xi(s,_.data,_.gc,y,g),x=b;else if(ot(g))for(d=0,h=g.length;d<h;++d)v=g[d],!tt(v)&&!ot(v)&&(y=xi(s,_.data,_.gc,y,v),x+=b);r.push(y),a.push(x),c=Math.max(y,c),u=Math.max(x,u)}Pg(o,n);const R=r.indexOf(c),k=a.indexOf(u),$=P=>({width:r[P]||0,height:a[P]||0});return{first:$(0),last:$(n-1),widest:$(R),highest:$(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,n){return NaN}getValueForPixel(t){}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const n=this._startPixel+t*this._length;return Hd(this._alignToPixels?ke(this.chart,n,0):n)}getDecimalForPixel(t){const n=(t-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:n}=this;return t<0&&n<0?n:t>0&&n>0?t:0}getContext(t){const n=this.ticks||[];if(t>=0&&t<n.length){const i=n[t];return i.$context||(i.$context=Dg(this.getContext(),t,i))}return this.$context||(this.$context=Fg(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,n=jt(this.labelRotation),i=Math.abs(Math.cos(n)),s=Math.abs(Math.sin(n)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const n=this.axis,i=this.chart,s=this.options,{grid:o,position:r,border:a}=s,l=o.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),d=ln(o),h=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,_=function(z){return ke(i,z,p)};let b,y,x,v,R,k,$,P,F,M,O,K;if(r==="top")b=_(this.bottom),k=this.bottom-d,P=b-m,M=_(t.top)+m,K=t.bottom;else if(r==="bottom")b=_(this.top),M=t.top,K=_(t.bottom)-m,k=b+m,P=this.top+d;else if(r==="left")b=_(this.right),R=this.right-d,$=b-m,F=_(t.left)+m,O=t.right;else if(r==="right")b=_(this.left),F=t.left,O=_(t.right)-m,R=b+m,$=this.left+d;else if(n==="x"){if(r==="center")b=_((t.top+t.bottom)/2+.5);else if(W(r)){const z=Object.keys(r)[0],B=r[z];b=_(this.chart.scales[z].getPixelForValue(B))}M=t.top,K=t.bottom,k=b+m,P=k+d}else if(n==="y"){if(r==="center")b=_((t.left+t.right)/2);else if(W(r)){const z=Object.keys(r)[0],B=r[z];b=_(this.chart.scales[z].getPixelForValue(B))}R=b-m,$=R-d,F=t.left,O=t.right}const Z=et(s.ticks.maxTicksLimit,f),T=Math.max(1,Math.ceil(f/Z));for(y=0;y<f;y+=T){const z=this.getContext(y),B=o.setContext(z),ft=a.setContext(z),st=B.lineWidth,wt=B.color,Xt=ft.dash||[],pt=ft.dashOffset,mt=B.tickWidth,Se=B.tickColor,ue=B.tickBorderDash||[],Et=B.tickBorderDashOffset;x=Ag(this,y,l),x!==void 0&&(v=ke(i,x,st),c?R=$=F=O=v:k=P=M=K=v,h.push({tx1:R,ty1:k,tx2:$,ty2:P,x1:F,y1:M,x2:O,y2:K,width:st,color:wt,borderDash:Xt,borderDashOffset:pt,tickWidth:mt,tickColor:Se,tickBorderDash:ue,tickBorderDashOffset:Et}))}return this._ticksLength=f,this._borderValue=b,h}_computeLabelItems(t){const n=this.axis,i=this.options,{position:s,ticks:o}=i,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:u,mirror:f}=o,d=ln(i.grid),h=d+u,g=f?-u:h,p=-jt(this.labelRotation),m=[];let _,b,y,x,v,R,k,$,P,F,M,O,K="middle";if(s==="top")R=this.bottom-g,k=this._getXAxisLabelAlignment();else if(s==="bottom")R=this.top+g,k=this._getXAxisLabelAlignment();else if(s==="left"){const T=this._getYAxisLabelAlignment(d);k=T.textAlign,v=T.x}else if(s==="right"){const T=this._getYAxisLabelAlignment(d);k=T.textAlign,v=T.x}else if(n==="x"){if(s==="center")R=(t.top+t.bottom)/2+h;else if(W(s)){const T=Object.keys(s)[0],z=s[T];R=this.chart.scales[T].getPixelForValue(z)+h}k=this._getXAxisLabelAlignment()}else if(n==="y"){if(s==="center")v=(t.left+t.right)/2-h;else if(W(s)){const T=Object.keys(s)[0],z=s[T];v=this.chart.scales[T].getPixelForValue(z)}k=this._getYAxisLabelAlignment(d).textAlign}n==="y"&&(l==="start"?K="top":l==="end"&&(K="bottom"));const Z=this._getLabelSizes();for(_=0,b=a.length;_<b;++_){y=a[_],x=y.label;const T=o.setContext(this.getContext(_));$=this.getPixelForTick(_)+o.labelOffset,P=this._resolveTickFontOptions(_),F=P.lineHeight,M=ot(x)?x.length:1;const z=M/2,B=T.color,ft=T.textStrokeColor,st=T.textStrokeWidth;let wt=k;r?(v=$,k==="inner"&&(_===b-1?wt=this.options.reverse?"left":"right":_===0?wt=this.options.reverse?"right":"left":wt="center"),s==="top"?c==="near"||p!==0?O=-M*F+F/2:c==="center"?O=-Z.highest.height/2-z*F+F:O=-Z.highest.height+F/2:c==="near"||p!==0?O=F/2:c==="center"?O=Z.highest.height/2-z*F:O=Z.highest.height-M*F,f&&(O*=-1),p!==0&&!T.showLabelBackdrop&&(v+=F/2*Math.sin(p))):(R=$,O=(1-M)*F/2);let Xt;if(T.showLabelBackdrop){const pt=$t(T.backdropPadding),mt=Z.heights[_],Se=Z.widths[_];let ue=O-pt.top,Et=0-pt.left;switch(K){case"middle":ue-=mt/2;break;case"bottom":ue-=mt;break}switch(k){case"center":Et-=Se/2;break;case"right":Et-=Se;break}Xt={left:Et,top:ue,width:Se+pt.width,height:mt+pt.height,color:T.backdropColor}}m.push({label:x,font:P,textOffset:O,options:{rotation:p,color:B,strokeColor:ft,strokeWidth:st,textAlign:wt,textBaseline:K,translation:[v,R],backdrop:Xt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:n}=this.options;if(-jt(this.labelRotation))return t==="top"?"left":"right";let s="center";return n.align==="start"?s="left":n.align==="end"?s="right":n.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){const{position:n,ticks:{crossAlign:i,mirror:s,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,u;return n==="left"?s?(u=this.right+o,i==="near"?c="left":i==="center"?(c="center",u+=l/2):(c="right",u+=l)):(u=this.right-a,i==="near"?c="right":i==="center"?(c="center",u-=l/2):(c="left",u=this.left)):n==="right"?s?(u=this.left+o,i==="near"?c="right":i==="center"?(c="center",u-=l/2):(c="left",u-=l)):(u=this.left+a,i==="near"?c="left":i==="center"?(c="center",u+=l/2):(c="right",u=this.right)):c="right",{textAlign:c,x:u}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:n},left:i,top:s,width:o,height:r}=this;n&&(t.save(),t.fillStyle=n,t.fillRect(i,s,o,r),t.restore())}getLineWidthForValue(t){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const s=this.ticks.findIndex(o=>o.value===t);return s>=0?n.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){const n=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,u)=>{!u.width||!u.color||(i.save(),i.lineWidth=u.width,i.strokeStyle=u.color,i.setLineDash(u.borderDash||[]),i.lineDashOffset=u.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(n.display)for(o=0,r=s.length;o<r;++o){const l=s[o];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:n,options:{border:i,grid:s}}=this,o=i.setContext(this.getContext()),r=i.display?o.width:0;if(!r)return;const a=s.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,u,f,d;this.isHorizontal()?(c=ke(t,this.left,r)-r/2,u=ke(t,this.right,a)+a/2,f=d=l):(f=ke(t,this.top,r)-r/2,d=ke(t,this.bottom,a)+a/2,c=u=l),n.save(),n.lineWidth=o.width,n.strokeStyle=o.color,n.beginPath(),n.moveTo(c,f),n.lineTo(u,d),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&il(i,s);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,u=r.textOffset;Sn(i,c,0,u,l,a)}s&&sl(i)}drawTitle(){const{ctx:t,options:{position:n,title:i,reverse:s}}=this;if(!i.display)return;const o=yt(i.font),r=$t(i.padding),a=i.align;let l=o.lineHeight/2;n==="bottom"||n==="center"||W(n)?(l+=r.bottom,ot(i.text)&&(l+=o.lineHeight*(i.text.length-1))):l+=r.top;const{titleX:c,titleY:u,maxWidth:f,rotation:d}=Eg(this,l,n,a);Sn(t,i.text,0,0,o,{color:i.color,maxWidth:f,rotation:d,textAlign:Og(a,n,s),textBaseline:"middle",translation:[c,u]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,n=t.ticks&&t.ticks.z||0,i=et(t.grid&&t.grid.z,-1),s=et(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Ve.prototype.draw?[{z:n,draw:o=>{this.draw(o)}}]:[{z:i,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:n,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const n=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let o,r;for(o=0,r=n.length;o<r;++o){const a=n[o];a[i]===this.id&&(!t||a.type===t)&&s.push(a)}return s}_resolveTickFontOptions(t){const n=this.options.ticks.setContext(this.getContext(t));return yt(n.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Wn{constructor(t,n,i){this.type=t,this.scope=n,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const n=Object.getPrototypeOf(t);let i;Ig(n)&&(i=this.register(n));const s=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in s||(s[o]=t,Lg(t,r,i),this.override&&ut.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const n=this.items,i=t.id,s=this.scope;i in n&&delete n[i],s&&i in ut[s]&&(delete ut[s][i],this.override&&delete Te[i])}}function Lg(e,t,n){const i=wn(Object.create(null),[n?ut.get(n):{},ut.get(t),e.defaults]);ut.set(t,i),e.defaultRoutes&&Tg(t,e.defaultRoutes),e.descriptors&&ut.describe(t,e.descriptors)}function Tg(e,t){Object.keys(t).forEach(n=>{const i=n.split("."),s=i.pop(),o=[e].concat(i).join("."),r=t[n].split("."),a=r.pop(),l=r.join(".");ut.route(o,s,l,a)})}function Ig(e){return"id"in e&&"defaults"in e}class Vg{constructor(){this.controllers=new Wn(Ue,"datasets",!0),this.elements=new Wn(Pn,"elements"),this.plugins=new Wn(Object,"plugins"),this.scales=new Wn(Ve,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,n,i){[...n].forEach(s=>{const o=i||this._getRegistryForType(s);i||o.isForType(s)||o===this.plugins&&s.id?this._exec(t,o,s):G(s,r=>{const a=i||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,n,i){const s=Ys(t);rt(i["before"+s],[],i),n[t](i),rt(i["after"+s],[],i)}_getRegistryForType(t){for(let n=0;n<this._typedRegistries.length;n++){const i=this._typedRegistries[n];if(i.isForType(t))return i}return this.plugins}_get(t,n,i){const s=n.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var Qt=new Vg;class zg{constructor(){this._init=[]}notify(t,n,i,s){n==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=s?this._descriptors(t).filter(s):this._descriptors(t),r=this._notify(o,t,n,i);return n==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,n,i,s){s=s||{};for(const o of t){const r=o.plugin,a=r[i],l=[n,s,o.options];if(rt(a,l,r)===!1&&s.cancelable)return!1}return!0}invalidate(){tt(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),n}_createDescriptors(t,n){const i=t&&t.config,s=et(i.options&&i.options.plugins,{}),o=Hg(i);return s===!1&&!n?[]:Bg(t,o,s,n)}_notifyStateChanges(t){const n=this._oldCache||[],i=this._cache,s=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(s(n,i),t,"stop"),this._notify(s(i,n),t,"start")}}function Hg(e){const t={},n=[],i=Object.keys(Qt.plugins.items);for(let o=0;o<i.length;o++)n.push(Qt.getPlugin(i[o]));const s=e.plugins||[];for(let o=0;o<s.length;o++){const r=s[o];n.indexOf(r)===-1&&(n.push(r),t[r.id]=!0)}return{plugins:n,localIds:t}}function Ng(e,t){return!t&&e===!1?null:e===!0?{}:e}function Bg(e,{plugins:t,localIds:n},i,s){const o=[],r=e.getContext();for(const a of t){const l=a.id,c=Ng(i[l],s);c!==null&&o.push({plugin:a,options:jg(e.config,{plugin:a,local:n[l]},c,r)})}return o}function jg(e,{plugin:t,local:n},i,s){const o=e.pluginScopeKeys(t),r=e.getOptionScopes(i,o);return n&&t.defaults&&r.push(t.defaults),e.createResolver(r,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Rs(e,t){const n=ut.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||n.indexAxis||"x"}function Wg(e,t){let n=e;return e==="_index_"?n=t:e==="_value_"&&(n=t==="x"?"y":"x"),n}function Gg(e,t){return e===t?"_index_":"_value_"}function qg(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function Ci(e,t){if(e==="x"||e==="y"||e==="r"||(e=t.axis||qg(t.position)||e.length>1&&Ci(e[0].toLowerCase(),t),e))return e;throw new Error(`Cannot determine type of '${name}' axis. Please provide 'axis' or 'position' option.`)}function Yg(e,t){const n=Te[e.type]||{scales:{}},i=t.scales||{},s=Rs(e.type,t),o=Object.create(null);return Object.keys(i).forEach(r=>{const a=i[r];if(!W(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Ci(r,a),c=Gg(l,s),u=n.scales||{};o[r]=pn(Object.create(null),[{axis:l},a,u[l],u[c]])}),e.data.datasets.forEach(r=>{const a=r.type||e.type,l=r.indexAxis||Rs(a,t),u=(Te[a]||{}).scales||{};Object.keys(u).forEach(f=>{const d=Wg(f,l),h=r[d+"AxisID"]||d;o[h]=o[h]||Object.create(null),pn(o[h],[{axis:d},i[h],u[f]])})}),Object.keys(o).forEach(r=>{const a=o[r];pn(a,[ut.scales[a.type],ut.scale])}),o}function _l(e){const t=e.options||(e.options={});t.plugins=et(t.plugins,{}),t.scales=Yg(e,t)}function bl(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function Ug(e){return e=e||{},e.data=bl(e.data),_l(e),e}const Cr=new Map,yl=new Set;function Gn(e,t){let n=Cr.get(e);return n||(n=t(),Cr.set(e,n),yl.add(n)),n}const cn=(e,t,n)=>{const i=Ze(t,n);i!==void 0&&e.add(i)};class Xg{constructor(t){this._config=Ug(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=bl(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),_l(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Gn(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,n){return Gn(`${t}.transition.${n}`,()=>[[`datasets.${t}.transitions.${n}`,`transitions.${n}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,n){return Gn(`${t}-${n}`,()=>[[`datasets.${t}.elements.${n}`,`datasets.${t}`,`elements.${n}`,""]])}pluginScopeKeys(t){const n=t.id,i=this.type;return Gn(`${i}-plugin-${n}`,()=>[[`plugins.${n}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,n){const i=this._scopeCache;let s=i.get(t);return(!s||n)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,n,i){const{options:s,type:o}=this,r=this._cachedScopes(t,i),a=r.get(n);if(a)return a;const l=new Set;n.forEach(u=>{t&&(l.add(t),u.forEach(f=>cn(l,t,f))),u.forEach(f=>cn(l,s,f)),u.forEach(f=>cn(l,Te[o]||{},f)),u.forEach(f=>cn(l,ut,f)),u.forEach(f=>cn(l,Cs,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),yl.has(n)&&r.set(n,c),c}chartOptionScopes(){const{options:t,type:n}=this;return[t,Te[n]||{},ut.datasets[n]||{},{type:n},ut,Cs]}resolveNamedOptions(t,n,i,s=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Mr(this._resolverCache,t,s);let l=r;if(Qg(r,n)){o.$shared=!1,i=we(i)?i():i;const c=this.createResolver(t,i,a);l=Je(r,i,c)}for(const c of n)o[c]=l[c];return o}createResolver(t,n,i=[""],s){const{resolver:o}=Mr(this._resolverCache,t,i);return W(n)?Je(o,n,void 0,s):o}}function Mr(e,t,n){let i=e.get(t);i||(i=new Map,e.set(t,i));const s=n.join();let o=i.get(s);return o||(o={resolver:Qs(t,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,o)),o}const Kg=e=>W(e)&&Object.getOwnPropertyNames(e).reduce((t,n)=>t||we(e[n]),!1);function Qg(e,t){const{isScriptable:n,isIndexable:i}=ol(e);for(const s of t){const o=n(s),r=i(s),a=(r||o)&&e[s];if(o&&(we(a)||Kg(a))||r&&ot(a))return!0}return!1}var Zg="4.2.1";const Jg=["top","bottom","left","right","chartArea"];function kr(e,t){return e==="top"||e==="bottom"||Jg.indexOf(e)===-1&&t==="x"}function Rr(e,t){return function(n,i){return n[e]===i[e]?n[t]-i[t]:n[e]-i[e]}}function $r(e){const t=e.chart,n=t.options.animation;t.notifyPlugins("afterRender"),rt(n&&n.onComplete,[e],t)}function tp(e){const t=e.chart,n=t.options.animation;rt(n&&n.onProgress,[e],t)}function vl(e){return cl()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const ei={},Ar=e=>{const t=vl(e);return Object.values(ei).filter(n=>n.canvas===t).pop()};function ep(e,t,n){const i=Object.keys(e);for(const s of i){const o=+s;if(o>=t){const r=e[s];delete e[s],(n>0||o>t)&&(e[o+n]=r)}}}function np(e,t,n,i){return!n||e.type==="mouseout"?null:i?t:e}function ip(e){const{xScale:t,yScale:n}=e;if(t&&n)return{left:t.left,right:t.right,top:n.top,bottom:n.bottom}}var de;let Ti=(de=class{static register(...t){Qt.add(...t),Pr()}static unregister(...t){Qt.remove(...t),Pr()}constructor(t,n){const i=this.config=new Xg(n),s=vl(t),o=Ar(s);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||xg(s)),this.platform.updateConfig(i);const a=this.platform.acquireContext(s,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,u=l&&l.width;if(this.id=Ad(),this.ctx=a,this.canvas=l,this.width=u,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new zg,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=qd(f=>this.update(f),r.resizeDelay||0),this._dataChanges=[],ei[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}se.listen(this,"complete",$r),se.listen(this,"progress",tp),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:n},width:i,height:s,_aspectRatio:o}=this;return tt(t)?n&&o?o:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Qt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():or(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return er(this.canvas,this.ctx),this}stop(){return se.stop(this),this}resize(t,n){se.running(this)?this._resizeBeforeDraw={width:t,height:n}:this._resize(t,n)}_resize(t,n){const i=this.options,s=this.canvas,o=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,t,n,o),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,or(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),rt(i.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};G(n,(i,s)=>{i.id=s})}buildOrUpdateScales(){const t=this.options,n=t.scales,i=this.scales,s=Object.keys(i).reduce((r,a)=>(r[a]=!1,r),{});let o=[];n&&(o=o.concat(Object.keys(n).map(r=>{const a=n[r],l=Ci(r,a),c=l==="r",u=l==="x";return{options:a,dposition:c?"chartArea":u?"bottom":"left",dtype:c?"radialLinear":u?"category":"linear"}}))),G(o,r=>{const a=r.options,l=a.id,c=Ci(l,a),u=et(a.type,r.dtype);(a.position===void 0||kr(a.position,c)!==kr(r.dposition))&&(a.position=r.dposition),s[l]=!0;let f=null;if(l in i&&i[l].type===u)f=i[l];else{const d=Qt.getScale(u);f=new d({id:l,type:u,ctx:this.ctx,chart:this}),i[f.id]=f}f.init(a,t)}),G(s,(r,a)=>{r||delete i[a]}),G(i,r=>{me.configure(this,r,r.options),me.addBox(this,r)})}_updateMetasets(){const t=this._metasets,n=this.data.datasets.length,i=t.length;if(t.sort((s,o)=>s.index-o.index),i>n){for(let s=n;s<i;++s)this._destroyDatasetMeta(s);t.splice(n,i-n)}this._sortedMetasets=t.slice(0).sort(Rr("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:n}}=this;t.length>n.length&&delete this._stacks,t.forEach((i,s)=>{n.filter(o=>o===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const t=[],n=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=n.length;i<s;i++){const o=n[i];let r=this.getDatasetMeta(i);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(i),r=this.getDatasetMeta(i)),r.type=a,r.indexAxis=o.indexAxis||Rs(a,this.options),r.order=o.order||0,r.index=i,r.label=""+o.label,r.visible=this.isDatasetVisible(i),r.controller)r.controller.updateIndex(i),r.controller.linkScales();else{const l=Qt.getController(a),{datasetElementType:c,dataElementType:u}=ut.datasets[a];Object.assign(l,{dataElementType:Qt.getElement(u),datasetElementType:c&&Qt.getElement(c)}),r.controller=new l(this,i),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){G(this.data.datasets,(t,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const n=this.config;n.update();const i=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,u=this.data.datasets.length;c<u;c++){const{controller:f}=this.getDatasetMeta(c),d=!s&&o.indexOf(f)===-1;f.buildOrUpdateElements(d),r=Math.max(+f.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),s||G(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Rr("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){G(this.scales,t=>{me.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,n=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!qo(n,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,n=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:o}of n){const r=i==="_removeElements"?-o:o;ep(t,s,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const n=this.data.datasets.length,i=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),s=i(0);for(let o=1;o<n;o++)if(!qo(s,i(o)))return;return Array.from(s).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;me.update(this,this.width,this.height,t);const n=this.chartArea,i=n.width<=0||n.height<=0;this._layers=[],G(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,o)=>{s._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let n=0,i=this.data.datasets.length;n<i;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,i=this.data.datasets.length;n<i;++n)this._updateDataset(n,we(t)?t({datasetIndex:n}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,n){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(n),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(se.has(this)?this.attached&&!se.running(this)&&se.start(this):(this.draw(),$r({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resize(i,s),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(this.chartArea);for(this._drawDatasets();t<n.length;++t)n[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const n=this._sortedMetasets,i=[];let s,o;for(s=0,o=n.length;s<o;++s){const r=n[s];(!t||r.visible)&&i.push(r)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let n=t.length-1;n>=0;--n)this._drawDataset(t[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const n=this.ctx,i=t._clip,s=!i.disabled,o=ip(t)||this.chartArea,r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(s&&il(n,{left:i.left===!1?0:o.left-i.left,right:i.right===!1?this.width:o.right+i.right,top:i.top===!1?0:o.top-i.top,bottom:i.bottom===!1?this.height:o.bottom+i.bottom}),t.controller.draw(),s&&sl(n),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return nl(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,n,i,s){const o=tg.modes[n];return typeof o=="function"?o(this,t,i,s):[]}getDatasetMeta(t){const n=this.data.datasets[t],i=this._metasets;let s=i.filter(o=>o&&o._dataset===n).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:t,_dataset:n,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=Ie(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const n=this.data.datasets[t];if(!n)return!1;const i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!n.hidden}setDatasetVisibility(t,n){const i=this.getDatasetMeta(t);i.hidden=!n}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,n,i){const s=i?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,s);Ut(n)?(o.data[n].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(o,{visible:i}),this.update(a=>a.datasetIndex===t?s:void 0))}hide(t,n){this._updateVisibility(t,n,!1)}show(t,n){this._updateVisibility(t,n,!0)}_destroyDatasetMeta(t){const n=this._metasets[t];n&&n.controller&&n.controller._destroy(),delete this._metasets[t]}_stop(){let t,n;for(this.stop(),se.remove(this),t=0,n=this.data.datasets.length;t<n;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:n}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),er(t,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete ei[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,n=this.platform,i=(o,r)=>{n.addEventListener(this,o,r),t[o]=r},s=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};G(this.options.events,o=>i(o,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,n=this.platform,i=(l,c)=>{n.addEventListener(this,l,c),t[l]=c},s=(l,c)=>{t[l]&&(n.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",o),i("detach",r)};r=()=>{this.attached=!1,s("resize",o),this._stop(),this._resize(0,0),i("attach",a)},n.isAttached(this.canvas)?a():r()}unbindEvents(){G(this._listeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._listeners={},G(this._responsiveListeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,n,i){const s=i?"set":"remove";let o,r,a,l;for(n==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[s+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const n=this._active||[],i=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!pi(i,n)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,n))}notifyPlugins(t,n,i){return this._plugins.notify(this,t,n,i)}isPluginEnabled(t){return this._plugins._cache.filter(n=>n.plugin.id===t).length===1}_updateHoverStyles(t,n,i){const s=this.options.hover,o=(l,c)=>l.filter(u=>!c.some(f=>u.datasetIndex===f.datasetIndex&&u.index===f.index)),r=o(n,t),a=i?t:o(t,n);r.length&&this.updateHoverStyle(r,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,n){const i={event:t,replay:n,cancelable:!0,inChartArea:this.isPointInArea(t)},s=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const o=this._handleEvent(t,n,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(o||i.changed)&&this.render(),this}_handleEvent(t,n,i){const{_active:s=[],options:o}=this,r=n,a=this._getActiveElements(t,s,i,r),l=Ld(t),c=np(t,this._lastEvent,i,l);i&&(this._lastEvent=null,rt(o.onHover,[t,a,this],this),l&&rt(o.onClick,[t,a,this],this));const u=!pi(a,s);return(u||n)&&(this._active=a,this._updateHoverStyles(a,s,n)),this._lastEvent=c,u}_getActiveElements(t,n,i,s){if(t.type==="mouseout")return[];if(!i)return n;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,s)}},H(de,"defaults",ut),H(de,"instances",ei),H(de,"overrides",Te),H(de,"registry",Qt),H(de,"version",Zg),H(de,"getChart",Ar),de);function Pr(){return G(Ti.instances,e=>e._plugins.invalidate())}function sp(e,t,n){const{startAngle:i,pixelMargin:s,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=s/a;e.beginPath(),e.arc(o,r,a,i-c,n+c),l>s?(c=s/l,e.arc(o,r,l,n+c,i-c,!0)):e.arc(o,r,s,n+lt,i-lt),e.closePath(),e.clip()}function op(e){return Ks(e,["outerStart","outerEnd","innerStart","innerEnd"])}function rp(e,t,n,i){const s=op(e.options.borderRadius),o=(n-t)/2,r=Math.min(o,i*t/2),a=l=>{const c=(n-Math.min(o,l))*i/2;return Mt(l,0,Math.min(o,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:Mt(s.innerStart,0,r),innerEnd:Mt(s.innerEnd,0,r)}}function Be(e,t,n,i){return{x:n+e*Math.cos(t),y:i+e*Math.sin(t)}}function Mi(e,t,n,i,s,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:u}=t,f=Math.max(t.outerRadius+i+n-c,0),d=u>0?u+i+n+c:0;let h=0;const g=s-l;if(i){const T=u>0?u-i:0,z=f>0?f-i:0,B=(T+z)/2,ft=B!==0?g*B/(B+i):g;h=(g-ft)/2}const p=Math.max(.001,g*f-n/ct)/f,m=(g-p)/2,_=l+m+h,b=s-m-h,{outerStart:y,outerEnd:x,innerStart:v,innerEnd:R}=rp(t,d,f,b-_),k=f-y,$=f-x,P=_+y/k,F=b-x/$,M=d+v,O=d+R,K=_+v/M,Z=b-R/O;if(e.beginPath(),o){const T=(P+F)/2;if(e.arc(r,a,f,P,T),e.arc(r,a,f,T,F),x>0){const st=Be($,F,r,a);e.arc(st.x,st.y,x,F,b+lt)}const z=Be(O,b,r,a);if(e.lineTo(z.x,z.y),R>0){const st=Be(O,Z,r,a);e.arc(st.x,st.y,R,b+lt,Z+Math.PI)}const B=(b-R/d+(_+v/d))/2;if(e.arc(r,a,d,b-R/d,B,!0),e.arc(r,a,d,B,_+v/d,!0),v>0){const st=Be(M,K,r,a);e.arc(st.x,st.y,v,K+Math.PI,_-lt)}const ft=Be(k,_,r,a);if(e.lineTo(ft.x,ft.y),y>0){const st=Be(k,P,r,a);e.arc(st.x,st.y,y,_-lt,P)}}else{e.moveTo(r,a);const T=Math.cos(P)*f+r,z=Math.sin(P)*f+a;e.lineTo(T,z);const B=Math.cos(F)*f+r,ft=Math.sin(F)*f+a;e.lineTo(B,ft)}e.closePath()}function ap(e,t,n,i,s){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Mi(e,t,n,i,l,s);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%at||at))}return Mi(e,t,n,i,l,s),e.fill(),l}function lp(e,t,n,i,s){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:u}=l,f=l.borderAlign==="inner";if(!c)return;f?(e.lineWidth=c*2,e.lineJoin=u||"round"):(e.lineWidth=c,e.lineJoin=u||"bevel");let d=t.endAngle;if(o){Mi(e,t,n,i,d,s);for(let h=0;h<o;++h)e.stroke();isNaN(a)||(d=r+(a%at||at))}f&&sp(e,t,d),o||(Mi(e,t,n,i,d,s),e.stroke())}class ni extends Pn{constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,n,i){const s=this.getProps(["x","y"],i),{angle:o,distance:r}=Xa(s,{x:t,y:n}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:u,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=this.options.spacing/2,g=et(f,l-a)>=at||vi(o,a,l),p=Nd(r,c+d,u+d);return g&&p}getCenterPoint(t){const{x:n,y:i,startAngle:s,endAngle:o,innerRadius:r,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:l,spacing:c}=this.options,u=(s+o)/2,f=(r+a+c+l)/2;return{x:n+Math.cos(u)*f,y:i+Math.sin(u)*f}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:n,circumference:i}=this,s=(n.offset||0)/4,o=(n.spacing||0)/2,r=n.circular;if(this.pixelMargin=n.borderAlign==="inner"?.33:0,this.fullCircles=i>at?Math.floor(i/at):0,i===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*s,Math.sin(a)*s);const l=1-Math.sin(Math.min(ct,i||0)),c=s*l;t.fillStyle=n.backgroundColor,t.strokeStyle=n.borderColor,ap(t,this,c,o,r),lp(t,this,c,o,r),t.restore()}}H(ni,"id","arc"),H(ni,"defaults",{borderAlign:"center",borderColor:"#fff",borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),H(ni,"defaultRoutes",{backgroundColor:"backgroundColor"});const $s=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Fr=$s.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function xl(e){return $s[e%$s.length]}function wl(e){return Fr[e%Fr.length]}function cp(e,t){return e.borderColor=xl(t),e.backgroundColor=wl(t),++t}function up(e,t){return e.backgroundColor=e.data.map(()=>xl(t++)),t}function fp(e,t){return e.backgroundColor=e.data.map(()=>wl(t++)),t}function dp(e){let t=0;return(n,i)=>{const s=e.getDatasetMeta(i).controller;s instanceof We?t=up(n,t):s instanceof Jn?t=fp(n,t):s&&(t=cp(n,t))}}function Dr(e){let t;for(t in e)if(e[t].borderColor||e[t].backgroundColor)return!0;return!1}function hp(e){return e&&(e.borderColor||e.backgroundColor)}var gp={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,t,n){if(!n.enabled)return;const{data:{datasets:i},options:s}=e.config,{elements:o}=s;if(!n.forceOverride&&(Dr(i)||hp(s)||o&&Dr(o)))return;const r=dp(e);i.forEach(r)}};class Sl extends Pn{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=n;const s=ot(i.text)?i.text.length:1;this._padding=$t(i.padding);const o=s*yt(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:n,left:i,bottom:s,right:o,options:r}=this,a=r.align;let l=0,c,u,f;return this.isHorizontal()?(u=mn(a,i,o),f=n+t,c=o-i):(r.position==="left"?(u=i+t,f=mn(a,s,n),l=ct*-.5):(u=o-t,f=mn(a,n,s),l=ct*.5),c=s-n),{titleX:u,titleY:f,maxWidth:c,rotation:l}}draw(){const t=this.ctx,n=this.options;if(!n.display)return;const i=yt(n.font),o=i.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Sn(t,n.text,0,0,i,{color:n.color,maxWidth:l,rotation:c,textAlign:Ja(n.align),textBaseline:"middle",translation:[r,a]})}}function pp(e,t){const n=new Sl({ctx:e.ctx,options:t,chart:e});me.configure(e,n,t),me.addBox(e,n),e.titleBlock=n}var mp={id:"title",_element:Sl,start(e,t,n){pp(e,n)},stop(e){const t=e.titleBlock;me.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){const i=e.titleBlock;me.configure(e,i,n),i.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const dn={average(e){if(!e.length)return!1;let t,n,i=0,s=0,o=0;for(t=0,n=e.length;t<n;++t){const r=e[t].element;if(r&&r.hasValue()){const a=r.tooltipPosition();i+=a.x,s+=a.y,++o}}return{x:i/o,y:s/o}},nearest(e,t){if(!e.length)return!1;let n=t.x,i=t.y,s=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){const l=e[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),u=zd(t,c);u<s&&(s=u,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,i=l.y}return{x:n,y:i}}};function Kt(e,t){return t&&(ot(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function oe(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function _p(e,t){const{element:n,datasetIndex:i,index:s}=t,o=e.getDatasetMeta(i).controller,{label:r,value:a}=o.getLabelAndValue(s);return{chart:e,label:r,parsed:o.getParsed(s),raw:e.data.datasets[i].data[s],formattedValue:a,dataset:o.getDataset(),dataIndex:s,datasetIndex:i,element:n}}function Or(e,t){const n=e.chart.ctx,{body:i,footer:s,title:o}=e,{boxWidth:r,boxHeight:a}=t,l=yt(t.bodyFont),c=yt(t.titleFont),u=yt(t.footerFont),f=o.length,d=s.length,h=i.length,g=$t(t.padding);let p=g.height,m=0,_=i.reduce((x,v)=>x+v.before.length+v.lines.length+v.after.length,0);if(_+=e.beforeBody.length+e.afterBody.length,f&&(p+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),_){const x=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=h*x+(_-h)*l.lineHeight+(_-1)*t.bodySpacing}d&&(p+=t.footerMarginTop+d*u.lineHeight+(d-1)*t.footerSpacing);let b=0;const y=function(x){m=Math.max(m,n.measureText(x).width+b)};return n.save(),n.font=c.string,G(e.title,y),n.font=l.string,G(e.beforeBody.concat(e.afterBody),y),b=t.displayColors?r+2+t.boxPadding:0,G(i,x=>{G(x.before,y),G(x.lines,y),G(x.after,y)}),b=0,n.font=u.string,G(e.footer,y),n.restore(),m+=g.width,{width:m,height:p}}function bp(e,t){const{y:n,height:i}=t;return n<i/2?"top":n>e.height-i/2?"bottom":"center"}function yp(e,t,n,i){const{x:s,width:o}=i,r=n.caretSize+n.caretPadding;if(e==="left"&&s+o+r>t.width||e==="right"&&s-o-r<0)return!0}function vp(e,t,n,i){const{x:s,width:o}=n,{width:r,chartArea:{left:a,right:l}}=e;let c="center";return i==="center"?c=s<=(a+l)/2?"left":"right":s<=o/2?c="left":s>=r-o/2&&(c="right"),yp(c,e,t,n)&&(c="center"),c}function Er(e,t,n){const i=n.yAlign||t.yAlign||bp(e,n);return{xAlign:n.xAlign||t.xAlign||vp(e,t,n,i),yAlign:i}}function xp(e,t){let{x:n,width:i}=e;return t==="right"?n-=i:t==="center"&&(n-=i/2),n}function wp(e,t,n){let{y:i,height:s}=e;return t==="top"?i+=n:t==="bottom"?i-=s+n:i-=s/2,i}function Lr(e,t,n,i){const{caretSize:s,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=n,c=s+o,{topLeft:u,topRight:f,bottomLeft:d,bottomRight:h}=yn(r);let g=xp(t,a);const p=wp(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(u,d)+s:a==="right"&&(g+=Math.max(f,h)+s),{x:Mt(g,0,i.width-t.width),y:Mt(p,0,i.height-t.height)}}function qn(e,t,n){const i=$t(n.padding);return t==="center"?e.x+e.width/2:t==="right"?e.x+e.width-i.right:e.x+i.left}function Tr(e){return Kt([],oe(e))}function Sp(e,t,n){return Ie(e,{tooltip:t,tooltipItems:n,type:"tooltip"})}function Ir(e,t){const n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const Cl={beforeTitle:ie,title(e){if(e.length>0){const t=e[0],n=t.chart.data.labels,i=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return n[t.dataIndex]}return""},afterTitle:ie,beforeBody:ie,beforeLabel:ie,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const n=e.formattedValue;return tt(n)||(t+=n),t},labelColor(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:ie,afterBody:ie,beforeFooter:ie,footer:ie,afterFooter:ie};function vt(e,t,n,i){const s=e[t].call(n,i);return typeof s>"u"?Cl[t].call(n,i):s}class As extends Pn{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const n=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&n.options.animation&&i.animations,o=new ul(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Sp(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,n){const{callbacks:i}=n,s=vt(i,"beforeTitle",this,t),o=vt(i,"title",this,t),r=vt(i,"afterTitle",this,t);let a=[];return a=Kt(a,oe(s)),a=Kt(a,oe(o)),a=Kt(a,oe(r)),a}getBeforeBody(t,n){return Tr(vt(n.callbacks,"beforeBody",this,t))}getBody(t,n){const{callbacks:i}=n,s=[];return G(t,o=>{const r={before:[],lines:[],after:[]},a=Ir(i,o);Kt(r.before,oe(vt(a,"beforeLabel",this,o))),Kt(r.lines,vt(a,"label",this,o)),Kt(r.after,oe(vt(a,"afterLabel",this,o))),s.push(r)}),s}getAfterBody(t,n){return Tr(vt(n.callbacks,"afterBody",this,t))}getFooter(t,n){const{callbacks:i}=n,s=vt(i,"beforeFooter",this,t),o=vt(i,"footer",this,t),r=vt(i,"afterFooter",this,t);let a=[];return a=Kt(a,oe(s)),a=Kt(a,oe(o)),a=Kt(a,oe(r)),a}_createItems(t){const n=this._active,i=this.chart.data,s=[],o=[],r=[];let a=[],l,c;for(l=0,c=n.length;l<c;++l)a.push(_p(this.chart,n[l]));return t.filter&&(a=a.filter((u,f,d)=>t.filter(u,f,d,i))),t.itemSort&&(a=a.sort((u,f)=>t.itemSort(u,f,i))),G(a,u=>{const f=Ir(t.callbacks,u);s.push(vt(f,"labelColor",this,u)),o.push(vt(f,"labelPointStyle",this,u)),r.push(vt(f,"labelTextColor",this,u))}),this.labelColors=s,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,n){const i=this.options.setContext(this.getContext()),s=this._active;let o,r=[];if(!s.length)this.opacity!==0&&(o={opacity:0});else{const a=dn[i.position].call(this,s,this._eventPosition);r=this._createItems(i),this.title=this.getTitle(r,i),this.beforeBody=this.getBeforeBody(r,i),this.body=this.getBody(r,i),this.afterBody=this.getAfterBody(r,i),this.footer=this.getFooter(r,i);const l=this._size=Or(this,i),c=Object.assign({},a,l),u=Er(this.chart,i,c),f=Lr(i,c,u,this.chart);this.xAlign=u.xAlign,this.yAlign=u.yAlign,o={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(t,n,i,s){const o=this.getCaretPosition(t,i,s);n.lineTo(o.x1,o.y1),n.lineTo(o.x2,o.y2),n.lineTo(o.x3,o.y3)}getCaretPosition(t,n,i){const{xAlign:s,yAlign:o}=this,{caretSize:r,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:u,bottomRight:f}=yn(a),{x:d,y:h}=t,{width:g,height:p}=n;let m,_,b,y,x,v;return o==="center"?(x=h+p/2,s==="left"?(m=d,_=m-r,y=x+r,v=x-r):(m=d+g,_=m+r,y=x-r,v=x+r),b=m):(s==="left"?_=d+Math.max(l,u)+r:s==="right"?_=d+g-Math.max(c,f)-r:_=this.caretX,o==="top"?(y=h,x=y-r,m=_-r,b=_+r):(y=h+p,x=y+r,m=_+r,b=_-r),v=y),{x1:m,x2:_,x3:b,y1:y,y2:x,y3:v}}drawTitle(t,n,i){const s=this.title,o=s.length;let r,a,l;if(o){const c=ts(i.rtl,this.x,this.width);for(t.x=qn(this,i.titleAlign,i),n.textAlign=c.textAlign(i.titleAlign),n.textBaseline="middle",r=yt(i.titleFont),a=i.titleSpacing,n.fillStyle=i.titleColor,n.font=r.string,l=0;l<o;++l)n.fillText(s[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,n,i,s,o){const r=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c,boxPadding:u}=o,f=yt(o.bodyFont),d=qn(this,"left",o),h=s.x(d),g=l<f.lineHeight?(f.lineHeight-l)/2:0,p=n.y+g;if(o.usePointStyle){const m={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},_=s.leftForLtr(h,c)+c/2,b=p+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,nr(t,m,_,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,nr(t,m,_,b)}else{t.lineWidth=W(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const m=s.leftForLtr(h,c-u),_=s.leftForLtr(s.xPlus(h,1),c-u-2),b=yn(r.borderRadius);Object.values(b).some(y=>y!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Ms(t,{x:m,y:p,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Ms(t,{x:_,y:p+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(m,p,c,l),t.strokeRect(m,p,c,l),t.fillStyle=r.backgroundColor,t.fillRect(_,p+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,n,i){const{body:s}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:u}=i,f=yt(i.bodyFont);let d=f.lineHeight,h=0;const g=ts(i.rtl,this.x,this.width),p=function($){n.fillText($,g.x(t.x+h),t.y+d/2),t.y+=d+o},m=g.textAlign(r);let _,b,y,x,v,R,k;for(n.textAlign=r,n.textBaseline="middle",n.font=f.string,t.x=qn(this,m,i),n.fillStyle=i.bodyColor,G(this.beforeBody,p),h=a&&m!=="right"?r==="center"?c/2+u:c+2+u:0,x=0,R=s.length;x<R;++x){for(_=s[x],b=this.labelTextColors[x],n.fillStyle=b,G(_.before,p),y=_.lines,a&&y.length&&(this._drawColorBox(n,t,x,g,i),d=Math.max(f.lineHeight,l)),v=0,k=y.length;v<k;++v)p(y[v]),d=f.lineHeight;G(_.after,p)}h=0,d=f.lineHeight,G(this.afterBody,p),t.y-=o}drawFooter(t,n,i){const s=this.footer,o=s.length;let r,a;if(o){const l=ts(i.rtl,this.x,this.width);for(t.x=qn(this,i.footerAlign,i),t.y+=i.footerMarginTop,n.textAlign=l.textAlign(i.footerAlign),n.textBaseline="middle",r=yt(i.footerFont),n.fillStyle=i.footerColor,n.font=r.string,a=0;a<o;++a)n.fillText(s[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+i.footerSpacing}}drawBackground(t,n,i,s){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:u}=i,{topLeft:f,topRight:d,bottomLeft:h,bottomRight:g}=yn(s.cornerRadius);n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,n.lineWidth=s.borderWidth,n.beginPath(),n.moveTo(a+f,l),r==="top"&&this.drawCaret(t,n,i,s),n.lineTo(a+c-d,l),n.quadraticCurveTo(a+c,l,a+c,l+d),r==="center"&&o==="right"&&this.drawCaret(t,n,i,s),n.lineTo(a+c,l+u-g),n.quadraticCurveTo(a+c,l+u,a+c-g,l+u),r==="bottom"&&this.drawCaret(t,n,i,s),n.lineTo(a+h,l+u),n.quadraticCurveTo(a,l+u,a,l+u-h),r==="center"&&o==="left"&&this.drawCaret(t,n,i,s),n.lineTo(a,l+f),n.quadraticCurveTo(a,l,a+f,l),n.closePath(),n.fill(),s.borderWidth>0&&n.stroke()}_updateAnimationTarget(t){const n=this.chart,i=this.$animations,s=i&&i.x,o=i&&i.y;if(s||o){const r=dn[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Or(this,t),l=Object.assign({},r,this._size),c=Er(n,t,l),u=Lr(t,l,c,n);(s._to!==u.x||o._to!==u.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,u))}}_willRender(){return!!this.opacity}draw(t){const n=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(n);const s={width:this.width,height:this.height},o={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const r=$t(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(o,t,s,n),Dh(t,n.textDirection),o.y+=r.top,this.drawTitle(o,t,n),this.drawBody(o,t,n),this.drawFooter(o,t,n),Oh(t,n.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,n){const i=this._active,s=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!pi(i,s),r=this._positionChanged(s,n);(o||r)&&(this._active=s,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,n,i=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,o=this._active||[],r=this._getActiveElements(t,o,n,i),a=this._positionChanged(r,t),l=n||!pi(r,o)||a;return l&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,n))),l}_getActiveElements(t,n,i,s){const o=this.options;if(t.type==="mouseout")return[];if(!s)return n;const r=this.chart.getElementsAtEventForMode(t,o.mode,o,i);return o.reverse&&r.reverse(),r}_positionChanged(t,n){const{caretX:i,caretY:s,options:o}=this,r=dn[o.position].call(this,t,n);return r!==!1&&(i!==r.x||s!==r.y)}}H(As,"positioners",dn);var Cp={id:"tooltip",_element:As,positioners:dn,afterInit(e,t,n){n&&(e.tooltip=new As({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const n={tooltip:t};if(e.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",n)}},afterEvent(e,t){if(e.tooltip){const n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Cl},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const Mp=(e,t,n,i)=>(typeof t=="string"?(n=e.push(t)-1,i.unshift({index:n,label:t})):isNaN(t)&&(n=null),n);function kp(e,t,n,i){const s=e.indexOf(t);if(s===-1)return Mp(e,t,n,i);const o=e.lastIndexOf(t);return s!==o?n:s}const Rp=(e,t)=>e===null?null:Mt(Math.round(e),0,t);function Vr(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class Ps extends Ve{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const n=this._addedLabels;if(n.length){const i=this.getLabels();for(const{index:s,label:o}of n)i[s]===o&&i.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,n){if(tt(t))return null;const i=this.getLabels();return n=isFinite(n)&&i[n]===t?n:kp(i,t,et(n,t),this._addedLabels),Rp(n,i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),n||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,n=this.max,i=this.options.offset,s=[];let o=this.getLabels();o=t===0&&n===o.length-1?o:o.slice(t,n+1),this._valueRange=Math.max(o.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let r=t;r<=n;r++)s.push({value:r});return s}getLabelForValue(t){return Vr.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}H(Ps,"id","category"),H(Ps,"defaults",{ticks:{callback:Vr}});function $p(e,t){const n=[],{bounds:s,step:o,min:r,max:a,precision:l,count:c,maxTicks:u,maxDigits:f,includeBounds:d}=e,h=o||1,g=u-1,{min:p,max:m}=t,_=!tt(r),b=!tt(a),y=!tt(c),x=(m-p)/(f+1);let v=Uo((m-p)/g/h)*h,R,k,$,P;if(v<1e-14&&!_&&!b)return[{value:p},{value:m}];P=Math.ceil(m/v)-Math.floor(p/v),P>g&&(v=Uo(P*v/g/h)*h),tt(l)||(R=Math.pow(10,l),v=Math.ceil(v*R)/R),s==="ticks"?(k=Math.floor(p/v)*v,$=Math.ceil(m/v)*v):(k=p,$=m),_&&b&&o&&Vd((a-r)/o,v/1e3)?(P=Math.round(Math.min((a-r)/v,u)),v=(a-r)/P,k=r,$=a):y?(k=_?r:k,$=b?a:$,P=c-1,v=($-k)/P):(P=($-k)/v,Zn(P,Math.round(P),v/1e3)?P=Math.round(P):P=Math.ceil(P));const F=Math.max(Xo(v),Xo(k));R=Math.pow(10,tt(l)?F:l),k=Math.round(k*R)/R,$=Math.round($*R)/R;let M=0;for(_&&(d&&k!==r?(n.push({value:r}),k<r&&M++,Zn(Math.round((k+M*v)*R)/R,r,zr(r,x,e))&&M++):k<r&&M++);M<P;++M)n.push({value:Math.round((k+M*v)*R)/R});return b&&d&&$!==a?n.length&&Zn(n[n.length-1].value,a,zr(a,x,e))?n[n.length-1].value=a:n.push({value:a}):(!b||$===a)&&n.push({value:$}),n}function zr(e,t,{horizontal:n,minRotation:i}){const s=jt(i),o=(n?Math.sin(s):Math.cos(s))||.001,r=.75*t*(""+e).length;return Math.min(t/o,r)}class ki extends Ve{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,n){return tt(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:n,maxDefined:i}=this.getUserBounds();let{min:s,max:o}=this;const r=l=>s=n?s:l,a=l=>o=i?o:l;if(t){const l=bi(s),c=bi(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(s===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(s-l)}this.min=s,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:n,stepSize:i}=t,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),n=n||11),n&&(s=Math.min(n,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,n=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},o=this._range||this,r=$p(s,o);return t.bounds==="ticks"&&Ua(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let n=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-n)/Math.max(t.length-1,1)/2;n-=s,i+=s}this._startValue=n,this._endValue=i,this._valueRange=i-n}getLabelForValue(t){return $n(t,this.chart.options.locale,this.options.ticks.format)}}class Hr extends ki{determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=dt(t)?t:0,this.max=dt(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),n=t?this.width:this.height,i=jt(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,o.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}H(Hr,"id","linear"),H(Hr,"defaults",{ticks:{callback:Ei.formatters.numeric}});const Mn=e=>Math.floor(pe(e)),$e=(e,t)=>Math.pow(10,Mn(e)+t);function Nr(e){return e/Math.pow(10,Mn(e))===1}function Br(e,t,n){const i=Math.pow(10,n),s=Math.floor(e/i);return Math.ceil(t/i)-s}function Ap(e,t){const n=t-e;let i=Mn(n);for(;Br(e,t,i)>10;)i++;for(;Br(e,t,i)<10;)i--;return Math.min(i,Mn(e))}function Pp(e,{min:t,max:n}){t=St(e.min,t);const i=[],s=Mn(t);let o=Ap(t,n),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=s>o?Math.pow(10,s):0,c=Math.round((t-l)*r)/r,u=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-u)/Math.pow(10,o)),d=St(e.min,Math.round((l+u+f*Math.pow(10,o))*r)/r);for(;d<n;)i.push({value:d,major:Nr(d),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(o++,f=2,r=o>=0?1:r),d=Math.round((l+u+f*Math.pow(10,o))*r)/r;const h=St(e.max,d);return i.push({value:h,major:Nr(h),significand:f}),i}class jr extends Ve{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,n){const i=ki.prototype.parse.apply(this,[t,n]);if(i===0){this._zero=!0;return}return dt(i)&&i>0?i:null}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=dt(t)?Math.max(0,t):null,this.max=dt(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!dt(this._userMin)&&(this.min=t===$e(this.min,0)?$e(this.min,-1):$e(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let i=this.min,s=this.max;const o=a=>i=t?i:a,r=a=>s=n?s:a;i===s&&(i<=0?(o(1),r(10)):(o($e(i,-1)),r($e(s,1)))),i<=0&&o($e(s,-1)),s<=0&&r($e(i,1)),this.min=i,this.max=s}buildTicks(){const t=this.options,n={min:this._userMin,max:this._userMax},i=Pp(n,this);return t.bounds==="ticks"&&Ua(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":$n(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=pe(t),this._valueRange=pe(this.max)-pe(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(pe(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const n=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+n*this._valueRange)}}H(jr,"id","logarithmic"),H(jr,"defaults",{ticks:{callback:Ei.formatters.logarithmic,major:{enabled:!0}}});function Fs(e){const t=e.ticks;if(t.display&&e.display){const n=$t(t.backdropPadding);return et(t.font&&t.font.size,ut.font.size)+n.height}return 0}function Fp(e,t,n){return n=ot(n)?n:[n],{w:nh(e,t.string,n),h:n.length*t.lineHeight}}function Wr(e,t,n,i,s){return e===i||e===s?{start:t-n/2,end:t+n/2}:e<i||e>s?{start:t-n,end:t}:{start:t,end:t+n}}function Dp(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),i=[],s=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?ct/o:0;for(let l=0;l<o;l++){const c=r.setContext(e.getPointLabelContext(l));s[l]=c.padding;const u=e.getPointPosition(l,e.drawingArea+s[l],a),f=yt(c.font),d=Fp(e.ctx,f,e._pointLabels[l]);i[l]=d;const h=Zt(e.getIndexAngle(l)+a),g=Math.round(Us(h)),p=Wr(g,u.x,d.w,0,180),m=Wr(g,u.y,d.h,90,270);Op(n,t,h,p,m)}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=Ep(e,i,s)}function Op(e,t,n,i,s){const o=Math.abs(Math.sin(n)),r=Math.abs(Math.cos(n));let a=0,l=0;i.start<t.l?(a=(t.l-i.start)/o,e.l=Math.min(e.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/o,e.r=Math.max(e.r,t.r+a)),s.start<t.t?(l=(t.t-s.start)/r,e.t=Math.min(e.t,t.t-l)):s.end>t.b&&(l=(s.end-t.b)/r,e.b=Math.max(e.b,t.b+l))}function Ep(e,t,n){const i=[],s=e._pointLabels.length,o=e.options,r=Fs(o)/2,a=e.drawingArea,l=o.pointLabels.centerPointLabels?ct/s:0;for(let c=0;c<s;c++){const u=e.getPointPosition(c,a+r+n[c],l),f=Math.round(Us(Zt(u.angle+lt))),d=t[c],h=Ip(u.y,d.h,f),g=Lp(f),p=Tp(u.x,d.w,g);i.push({x:u.x,y:h,textAlign:g,left:p,top:h,right:p+d.w,bottom:h+d.h})}return i}function Lp(e){return e===0||e===180?"center":e<180?"left":"right"}function Tp(e,t,n){return n==="right"?e-=t:n==="center"&&(e-=t/2),e}function Ip(e,t,n){return n===90||n===270?e-=t/2:(n>270||n<90)&&(e-=t),e}function Vp(e,t){const{ctx:n,options:{pointLabels:i}}=e;for(let s=t-1;s>=0;s--){const o=i.setContext(e.getPointLabelContext(s)),r=yt(o.font),{x:a,y:l,textAlign:c,left:u,top:f,right:d,bottom:h}=e._pointLabelItems[s],{backdropColor:g}=o;if(!tt(g)){const p=yn(o.borderRadius),m=$t(o.backdropPadding);n.fillStyle=g;const _=u-m.left,b=f-m.top,y=d-u+m.width,x=h-f+m.height;Object.values(p).some(v=>v!==0)?(n.beginPath(),Ms(n,{x:_,y:b,w:y,h:x,radius:p}),n.fill()):n.fillRect(_,b,y,x)}Sn(n,e._pointLabels[s],a,l+r.lineHeight/2,r,{color:o.color,textAlign:c,textBaseline:"middle"})}}function Ml(e,t,n,i){const{ctx:s}=e;if(n)s.arc(e.xCenter,e.yCenter,t,0,at);else{let o=e.getPointPosition(0,t);s.moveTo(o.x,o.y);for(let r=1;r<i;r++)o=e.getPointPosition(r,t),s.lineTo(o.x,o.y)}}function zp(e,t,n,i,s){const o=e.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!i||!a||!l||n<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(s.dash),o.lineDashOffset=s.dashOffset,o.beginPath(),Ml(e,n,r,i),o.closePath(),o.stroke(),o.restore())}function Hp(e,t,n){return Ie(e,{label:n,index:t,type:"pointLabel"})}class Yn extends ki{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=$t(Fs(this.options)/2),n=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+n/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(n,i)/2)}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!1);this.min=dt(t)&&!isNaN(t)?t:0,this.max=dt(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Fs(this.options))}generateTickLabels(t){ki.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((n,i)=>{const s=rt(this.options.pointLabels.callback,[n,i],this);return s||s===0?s:""}).filter((n,i)=>this.chart.getDataVisibility(i))}fit(){const t=this.options;t.display&&t.pointLabels.display?Dp(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,n,i,s){this.xCenter+=Math.floor((t-n)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,n,i,s))}getIndexAngle(t){const n=at/(this._pointLabels.length||1),i=this.options.startAngle||0;return Zt(t*n+jt(i))}getDistanceFromCenterForValue(t){if(tt(t))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*n:(t-this.min)*n}getValueForDistanceFromCenter(t){if(tt(t))return NaN;const n=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(t){const n=this._pointLabels||[];if(t>=0&&t<n.length){const i=n[t];return Hp(this.getContext(),t,i)}}getPointPosition(t,n,i=0){const s=this.getIndexAngle(t)-lt+i;return{x:Math.cos(s)*n+this.xCenter,y:Math.sin(s)*n+this.yCenter,angle:s}}getPointPositionForValue(t,n){return this.getPointPosition(t,this.getDistanceFromCenterForValue(n))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:n,top:i,right:s,bottom:o}=this._pointLabelItems[t];return{left:n,top:i,right:s,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:n}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),Ml(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,n=this.options,{angleLines:i,grid:s,border:o}=n,r=this._pointLabels.length;let a,l,c;if(n.pointLabels.display&&Vp(this,r),s.display&&this.ticks.forEach((u,f)=>{if(f!==0){l=this.getDistanceFromCenterForValue(u.value);const d=this.getContext(f),h=s.setContext(d),g=o.setContext(d);zp(this,h,l,r,g)}}),i.display){for(t.save(),a=r-1;a>=0;a--){const u=i.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:d}=u;!d||!f||(t.lineWidth=d,t.strokeStyle=f,t.setLineDash(u.borderDash),t.lineDashOffset=u.borderDashOffset,l=this.getDistanceFromCenterForValue(n.ticks.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,n=this.options,i=n.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&!n.reverse)return;const c=i.setContext(this.getContext(l)),u=yt(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=u.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=$t(c.backdropPadding);t.fillRect(-r/2-f.left,-o-u.size/2-f.top,r+f.width,u.size+f.height)}Sn(t,a.label,0,-o,u,{color:c.color})}),t.restore()}drawTitle(){}}H(Yn,"id","radialLinear"),H(Yn,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ei.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),H(Yn,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),H(Yn,"descriptors",{angleLines:{_fallback:"grid"}});const Ii={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},xt=Object.keys(Ii);function Np(e,t){return e-t}function Gr(e,t){if(tt(t))return null;const n=e._adapter,{parser:i,round:s,isoWeekday:o}=e._parseOpts;let r=t;return typeof i=="function"&&(r=i(r)),dt(r)||(r=typeof i=="string"?n.parse(r,i):n.parse(r)),r===null?null:(s&&(r=s==="week"&&(yi(o)||o===!0)?n.startOf(r,"isoWeek",o):n.startOf(r,s)),+r)}function qr(e,t,n,i){const s=xt.length;for(let o=xt.indexOf(e);o<s-1;++o){const r=Ii[xt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((n-t)/(a*r.size))<=i)return xt[o]}return xt[s-1]}function Bp(e,t,n,i,s){for(let o=xt.length-1;o>=xt.indexOf(n);o--){const r=xt[o];if(Ii[r].common&&e._adapter.diff(s,i,r)>=t-1)return r}return xt[n?xt.indexOf(n):0]}function jp(e){for(let t=xt.indexOf(e)+1,n=xt.length;t<n;++t)if(Ii[xt[t]].common)return xt[t]}function Yr(e,t,n){if(!n)e[t]=!0;else if(n.length){const{lo:i,hi:s}=Xs(n,t),o=n[i]>=t?n[i]:n[s];e[o]=!0}}function Wp(e,t,n,i){const s=e._adapter,o=+s.startOf(t[0].value,i),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+s.add(a,1,i))l=n[a],l>=0&&(t[l].major=!0);return t}function Ur(e,t,n){const i=[],s={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],s[a]=r,i.push({value:a,major:!1});return o===0||!n?i:Wp(e,i,s,n)}class Ri extends Ve{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,n={}){const i=t.time||(t.time={}),s=this._adapter=new Xh._date(t.adapters.date);s.init(n),pn(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=n.normalized}parse(t,n){return t===void 0?null:Gr(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,n=this._adapter,i=t.time.unit||"day";let{min:s,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=dt(s)&&!isNaN(s)?s:+n.startOf(Date.now(),i),o=dt(o)&&!isNaN(o)?o:+n.endOf(Date.now(),i)+1,this.min=Math.min(s,o-1),this.max=Math.max(s+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(n=t[0],i=t[t.length-1]),{min:n,max:i}}buildTicks(){const t=this.options,n=t.time,i=t.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const o=this.min,r=this.max,a=jd(s,o,r);return this._unit=n.unit||(i.autoSkip?qr(n.minUnit,this.min,this.max,this._getLabelCapacity(o)):Bp(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:jp(this._unit),this.initOffsets(s),t.reverse&&a.reverse(),Ur(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let n=0,i=0,s,o;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?n=1-s:n=(this.getDecimalForValue(t[1])-s)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?i=o:i=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;n=Mt(n,0,r),i=Mt(i,0,r),this._offsets={start:n,end:i,factor:1/(n+1+i)}}_generate(){const t=this._adapter,n=this.min,i=this.max,s=this.options,o=s.time,r=o.unit||qr(o.minUnit,n,i,this._getLabelCapacity(n)),a=et(s.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=yi(l)||l===!0,u={};let f=n,d,h;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":r),t.diff(i,n,r)>1e5*a)throw new Error(n+" and "+i+" are too far apart with stepSize of "+a+" "+r);const g=s.ticks.source==="data"&&this.getDataTimestamps();for(d=f,h=0;d<i;d=+t.add(d,a,r),h++)Yr(u,d,g);return(d===i||s.bounds==="ticks"||h===1)&&Yr(u,d,g),Object.keys(u).sort((p,m)=>p-m).map(p=>+p)}getLabelForValue(t){const n=this._adapter,i=this.options.time;return i.tooltipFormat?n.format(t,i.tooltipFormat):n.format(t,i.displayFormats.datetime)}format(t,n){const s=this.options.time.displayFormats,o=this._unit,r=n||s[o];return this._adapter.format(t,r)}_tickFormatFunction(t,n,i,s){const o=this.options,r=o.ticks.callback;if(r)return rt(r,[t,n,i],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,u=l&&a[l],f=c&&a[c],d=i[n],h=c&&f&&d&&d.major;return this._adapter.format(t,s||(h?f:u))}generateTickLabels(t){let n,i,s;for(n=0,i=t.length;n<i;++n)s=t[n],s.label=this._tickFormatFunction(s.value,n,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const n=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((n.start+i)*n.factor)}getValueForPixel(t){const n=this._offsets,i=this.getDecimalForPixel(t)/n.factor-n.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const n=this.options.ticks,i=this.ctx.measureText(t).width,s=jt(this.isHorizontal()?n.maxRotation:n.minRotation),o=Math.cos(s),r=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*o+a*r,h:i*r+a*o}}_getLabelCapacity(t){const n=this.options.time,i=n.displayFormats,s=i[n.unit]||i.millisecond,o=this._tickFormatFunction(t,0,Ur(this,[t],this._majorUnit),s),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],n,i;if(t.length)return t;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(n=0,i=s.length;n<i;++n)t=t.concat(s[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let n,i;if(t.length)return t;const s=this.getLabels();for(n=0,i=s.length;n<i;++n)t.push(Gr(this,s[n]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Gd(t.sort(Np))}}H(Ri,"id","time"),H(Ri,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Un(e,t,n){let i=0,s=e.length-1,o,r,a,l;n?(t>=e[i].pos&&t<=e[s].pos&&({lo:i,hi:s}=Ss(e,"pos",t)),{pos:o,time:a}=e[i],{pos:r,time:l}=e[s]):(t>=e[i].time&&t<=e[s].time&&({lo:i,hi:s}=Ss(e,"time",t)),{time:o,pos:a}=e[i],{time:r,pos:l}=e[s]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class Xr extends Ri{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(t);this._minPos=Un(n,this.min),this._tableRange=Un(n,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:n,max:i}=this,s=[],o=[];let r,a,l,c,u;for(r=0,a=t.length;r<a;++r)c=t[r],c>=n&&c<=i&&s.push(c);if(s.length<2)return[{time:n,pos:0},{time:i,pos:1}];for(r=0,a=s.length;r<a;++r)u=s[r+1],l=s[r-1],c=s[r],Math.round((u+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const n=this.getDataTimestamps(),i=this.getLabelTimestamps();return n.length&&i.length?t=this.normalize(n.concat(i)):t=n.length?n:i,t=this._cache.all=t,t}getDecimalForValue(t){return(Un(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const n=this._offsets,i=this.getDecimalForPixel(t)/n.factor-n.end;return Un(this._table,i*this._tableRange+this._minPos,!0)}}H(Xr,"id","timeseries"),H(Xr,"defaults",Ri.defaults);const Kr=/^on/,kl=[];Object.keys(globalThis).forEach(e=>{Kr.test(e)&&kl.push(e.replace(Kr,""))});function Rl(e){const t=Oe,n=[];function i(s){ec(t,s)}Ai(()=>{const s=e();kl.forEach(s instanceof Element?o=>n.push(It(s,o,i)):o=>n.push(s.$on(o,i)))}),en(()=>{for(;n.length;)n.pop()()})}function Gp(e){let t,n=[e[1]],i={};for(let s=0;s<n.length;s+=1)i=N(i,n[s]);return{c(){t=A("canvas"),Bl(t,i)},m(s,o){it(s,t,o),e[8](t)},p:ht,i:ht,o:ht,d(s){s&&U(t),e[8](null)}}}function qp(e){let{data:t,type:n,options:i,plugins:s,children:o,$$scope:r,$$slots:a,...l}=e;return l}function Yp(e,t,n){let{type:i}=t,{data:s={datasets:[]}}=t,{options:o={}}=t,{plugins:r=[]}=t,{updateMode:a=void 0}=t,{chart:l=null}=t,c,u=qp(t);Ai(()=>{n(2,l=new Ti(c,{type:i,data:s,options:o,plugins:r}))}),Zl(()=>{l&&(n(2,l.data=s,l),Object.assign(l.options,o),l.update(a))}),en(()=>{l&&l.destroy(),n(2,l=null)}),Rl(()=>c);function f(d){Ke[d?"unshift":"push"](()=>{c=d,n(0,c)})}return e.$$set=d=>{n(9,t=N(N({},t),nt(d))),"type"in d&&n(3,i=d.type),"data"in d&&n(4,s=d.data),"options"in d&&n(5,o=d.options),"plugins"in d&&n(6,r=d.plugins),"updateMode"in d&&n(7,a=d.updateMode),"chart"in d&&n(2,l=d.chart)},t=nt(t),[c,u,l,i,s,o,r,a,f]}let Up=class extends X{constructor(t){super(),Q(this,t,Yp,Gp,Y,{type:3,data:4,options:5,plugins:6,updateMode:7,chart:2})}};function Xp(e){let t,n,i;const s=[{type:"pie"},e[1]];function o(a){e[4](a)}let r={};for(let a=0;a<s.length;a+=1)r=N(r,s[a]);return e[0]!==void 0&&(r.chart=e[0]),t=new Up({props:r}),e[3](t),Ke.push(()=>da(t,"chart",o)),{c(){V(t.$$.fragment)},m(a,l){E(t,a,l),i=!0},p(a,[l]){const c=l&2?zt(s,[s[0],ee(a[1])]):{};!n&&l&1&&(n=!0,c.chart=a[0],ca(()=>n=!1)),t.$set(c)},i(a){i||(S(t.$$.fragment,a),i=!0)},o(a){C(t.$$.fragment,a),i=!1},d(a){e[3](null),L(t,a)}}}function Kp(e,t,n){Ti.register(ks);let{chart:i=null}=t,s,o;Rl(()=>o);function r(l){Ke[l?"unshift":"push"](()=>{o=l,n(2,o)})}function a(l){i=l,n(0,i)}return e.$$set=l=>{n(5,t=N(N({},t),nt(l))),"chart"in l&&n(0,i=l.chart)},e.$$.update=()=>{n(1,s=t)},t=nt(t),[i,s,o,r,a]}class Qp extends X{constructor(t){super(),Q(this,t,Kp,Xp,Y,{chart:0})}}function Zp(e){let t,n;return t=new Qp({props:{class:"self-center",width:256,height:256,data:{labels:e[0].labels,datasets:[{data:e[0].data}]},options:{maintainAspectRatio:!1,responsive:!1,parsing:{key:"time"},animation:!1,plugins:{tooltip:{callbacks:{label:Jp}}}}}}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},p(i,[s]){const o={};s&1&&(o.data={labels:i[0].labels,datasets:[{data:i[0].data}]}),t.$set(o)},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}const Jp=e=>`Queries: ${e.raw.queries}, Time: ${e.raw.time.toFixed(4)} ms`;function tm(e,t,n){let i;return gt(e,gs,s=>n(0,i=s)),Ti.register(mp,Cp,ni,Ps,gp),[i]}class em extends X{constructor(t){super(),Q(this,t,tm,Zp,Y,{})}}function Qr(e,t,n){const i=e.slice();return i[5]=t[n],i}function Zr(e){let t,n=e[5]+"",i,s,o,r;function a(){return e[4](e[5])}return{c(){t=A("button"),i=J(n),s=q(),D(t,"class","bg-dark-600 p-3 border-[1px] outline-none border-transparent focus-visible:border-cyan-600 text-left hover:bg-dark-400 rounded-md")},m(l,c){it(l,t,c),w(t,i),w(t,s),o||(r=It(t,"click",a),o=!0)},p(l,c){e=l,c&2&&n!==(n=e[5]+"")&&Wt(i,n)},d(l){l&&U(t),o=!1,r()}}}function nm(e){let t,n,i,s,o,r,a,l,c,u,f,d,h,g,p,m,_,b,y,x,v,R,k,$=e[2].queries+"",P,F,M,O,K=e[2].timeQuerying.toFixed(4)+"",Z,T,z,B,ft,st=e[2].slowQueries+"",wt,Xt,pt,mt;a=new rd({});function Se(j){e[3](j)}let ue={icon:Ba};e[0]!==void 0&&(ue.value=e[0]),c=new Kf({props:ue}),Ke.push(()=>da(c,"value",Se));let Et=e[1],_t=[];for(let j=0;j<Et.length;j+=1)_t[j]=Zr(Qr(e,Et,j));return y=new ed({}),pt=new em({}),{c(){t=A("div"),n=A("div"),i=A("div"),s=A("div"),o=A("p"),o.textContent="Resources",r=q(),V(a.$$.fragment),l=q(),V(c.$$.fragment),f=q(),d=A("div");for(let j=0;j<_t.length;j+=1)_t[j].c();h=q(),g=A("div"),p=A("div"),m=A("div"),_=A("p"),_.textContent="General data",b=q(),V(y.$$.fragment),x=q(),v=A("div"),R=A("p"),k=J("Queries: "),P=J($),F=q(),M=A("p"),O=J("Time querying: "),Z=J(K),T=J(" ms"),z=q(),B=A("p"),ft=J("Slow queries: "),wt=J(st),Xt=q(),V(pt.$$.fragment),D(o,"class","text-2xl"),D(s,"class","flex gap-3 items-center "),D(i,"class","pr-4 flex justify-between items-center"),D(d,"class","flex flex-col gap-3 mt-6 overflow-y-auto pr-4"),D(n,"class","bg-dark-700 p-4 pr-0 flex flex-col w-2/3 rounded-md"),D(_,"class","text-2xl"),D(m,"class","flex gap-3 items-center mb-4"),D(B,"class","text-yellow-500"),D(v,"class","flex flex-col text-dark-50"),D(p,"class","flex flex-col"),D(g,"class","bg-dark-700 p-4 flex flex-col justify-between w-1/3 rounded-md"),D(t,"class","p-2 w-full h-full flex justify-between gap-2")},m(j,fe){it(j,t,fe),w(t,n),w(n,i),w(i,s),w(s,o),w(s,r),E(a,s,null),w(i,l),E(c,i,null),w(n,f),w(n,d);for(let Ce=0;Ce<_t.length;Ce+=1)_t[Ce]&&_t[Ce].m(d,null);w(t,h),w(t,g),w(g,p),w(p,m),w(m,_),w(m,b),E(y,m,null),w(p,x),w(p,v),w(v,R),w(R,k),w(R,P),w(v,F),w(v,M),w(M,O),w(M,Z),w(M,T),w(v,z),w(v,B),w(B,ft),w(B,wt),w(g,Xt),E(pt,g,null),mt=!0},p(j,[fe]){const Ce={};if(!u&&fe&1&&(u=!0,Ce.value=j[0],ca(()=>u=!1)),c.$set(Ce),fe&2){Et=j[1];let Lt;for(Lt=0;Lt<Et.length;Lt+=1){const no=Qr(j,Et,Lt);_t[Lt]?_t[Lt].p(no,fe):(_t[Lt]=Zr(no),_t[Lt].c(),_t[Lt].m(d,null))}for(;Lt<_t.length;Lt+=1)_t[Lt].d(1);_t.length=Et.length}(!mt||fe&4)&&$!==($=j[2].queries+"")&&Wt(P,$),(!mt||fe&4)&&K!==(K=j[2].timeQuerying.toFixed(4)+"")&&Wt(Z,K),(!mt||fe&4)&&st!==(st=j[2].slowQueries+"")&&Wt(wt,st)},i(j){mt||(S(a.$$.fragment,j),S(c.$$.fragment,j),S(y.$$.fragment,j),S(pt.$$.fragment,j),mt=!0)},o(j){C(a.$$.fragment,j),C(c.$$.fragment,j),C(y.$$.fragment,j),C(pt.$$.fragment,j),mt=!1},d(j){j&&U(t),L(a),L(c),Xe(_t,j),L(y),L(pt)}}}function im(e,t,n){let i,s,o;gt(e,fs,l=>n(0,i=l)),gt(e,Xc,l=>n(1,s=l)),gt(e,hs,l=>n(2,o=l));function r(l){i=l,fs.set(i)}return[i,s,o,r,l=>Ee.goto(`/${l}`)]}class sm extends X{constructor(t){super(),Q(this,t,im,nm,Y,{})}}function Jr(e){let t,n,i,s,o,r,a;return i=new co({props:{path:"/",$$slots:{default:[om]},$$scope:{ctx:e}}}),o=new co({props:{path:"/:resource",$$slots:{default:[rm]},$$scope:{ctx:e}}}),{c(){t=A("main"),n=A("div"),V(i.$$.fragment),s=q(),V(o.$$.fragment),D(n,"class","bg-dark-800 flex h-[700px] w-[1200px] rounded-md text-white"),D(t,"class","font-main flex h-full w-full items-center justify-center")},m(l,c){it(l,t,c),w(t,n),E(i,n,null),w(n,s),E(o,n,null),a=!0},i(l){a||(S(i.$$.fragment,l),S(o.$$.fragment,l),Qe(()=>{a&&(r||(r=ai(t,Ao,{start:.95,duration:150},!0)),r.run(1))}),a=!0)},o(l){C(i.$$.fragment,l),C(o.$$.fragment,l),r||(r=ai(t,Ao,{start:.95,duration:150},!1)),r.run(0),a=!1},d(l){l&&U(t),L(i),L(o),l&&r&&r.end()}}}function om(e){let t,n;return t=new sm({}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function rm(e){let t,n;return t=new Yf({}),{c(){V(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function am(e){let t,n,i=e[0]&&Jr(e);return{c(){i&&i.c(),t=kn()},m(s,o){i&&i.m(s,o),it(s,t,o),n=!0},p(s,[o]){s[0]?i?o&1&&S(i,1):(i=Jr(s),i.c(),S(i,1),i.m(t.parentNode,t)):i&&(kt(),C(i,1,1,()=>{i=null}),Rt())},i(s){n||(S(i),n=!0)},o(s){C(i),n=!1},d(s){i&&i.d(s),s&&U(t)}}}function lm(e,t,n){let i,s,o,r;gt(e,ji,l=>n(0,i=l)),gt(e,gs,l=>n(1,s=l)),gt(e,hs,l=>n(2,o=l)),gt(e,ds,l=>n(3,r=l)),Ee.mode.hash(),Ee.goto("/"),za("openUI",l=>{bt(ji,i=!0,i),bt(ds,r=l.resources,r),bt(hs,o={queries:l.totalQueries,slowQueries:l.slowQueries,timeQuerying:l.totalTime},o),bt(gs,s={labels:l.chartData.labels,data:l.chartData.data},s)}),Na([{action:"openUI",data:{resources:["ox_core","oxmysql","ox_inventory","ox_doorlock","ox_lib","ox_vehicleshop","ox_target"],slowQueries:13,totalQueries:332,totalTime:230123,chartData:{labels:["oxmysql","ox_core","ox_inventory","ox_doorlock"],data:[{queries:25,time:133},{queries:5,time:12},{queries:3,time:2},{queries:72,time:133}]}}}]);const a=l=>{l.key==="Escape"&&(bt(ji,i=!1,i),ba("exit"))};return e.$$.update=()=>{e.$$.dirty&1&&(i?window.addEventListener("keydown",a):window.removeEventListener("keydown",a))},[i]}class cm extends X{constructor(t){super(),Q(this,t,lm,am,Y,{})}}new cm({target:document.getElementById("app")});if(Ha()){const e=document.getElementById("app");e.style.backgroundImage='url("https://i.imgur.com/3pzRj9n.png")',e.style.backgroundSize="cover",e.style.backgroundRepeat="no-repeat",e.style.backgroundPosition="center"}

<?xml version="1.0" encoding="UTF-8"?>

<CWeaponComponentInfoBlob>
  <Data>
  </Data>
  <Infos>
    <!-- flashlight UV head -->
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_FLASHLIGHT_LIGHT_UV</Name>
      <Model>w_me_flashlight_flash_uv</Model>
      <LocName>WCT_FLASH</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="7.200000" />
      <!-- <MainLightColor value="0xFFB1C8FF" /> -->
      <MainLightColor value="0xFF9E00FF" /> <!-- ARGB -->
      <MainLightRange value="25.000000" />
      <MainLightFalloffExponent value="32.000000" />
      <MainLightInnerAngle value="0.000000" />
      <MainLightOuterAngle value="25.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.500000" />
      <MainLightVolumeSize value="0.150000" />
      <MainLightVolumeExponent value="70.000000" />
      <MainLightVolumeOuterColor value="0xFF9E00FF" />
      <MainLightShadowFadeDistance value="15.000000" />
      <MainLightSpecularFadeDistance value="15.000000" />
      <SecondaryLightIntensity value="5.000000" />
      <SecondaryLightColor value="0xFF9E00FF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="60.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0x00FFFFFF" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="8.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
      <ToggleWhenAiming value="true" />
    </Item>
    <!-- xmas 2023 candy cane skins -->
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_CANDYCANE_VARMOD_BL_01</Name>
      <Model>W_ME_Candy_XM3_BL_01</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_CANDYCANE_VARMOD_BL_02</Name>
      <Model>W_ME_Candy_XM3_BL_02</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_CANDYCANE_VARMOD_BL_03</Name>
      <Model>W_ME_Candy_XM3_BL_03</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_CANDYCANE_VARMOD_BL_04</Name>
      <Model>W_ME_Candy_XM3_BL_04</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <!-- /xmas 2023 candy cane skins -->
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3</Name>
      <Model>W_ME_Knife_XM3</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_01</Name>
      <Model>W_ME_Knife_XM3_01</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_02</Name>
      <Model>W_ME_Knife_XM3_02</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_03</Name>
      <Model>W_ME_Knife_XM3_03</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_04</Name>
      <Model>W_ME_Knife_XM3_04</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_05</Name>
      <Model>W_ME_Knife_XM3_05</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_06</Name>
      <Model>W_ME_Knife_XM3_06</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_07</Name>
      <Model>W_ME_Knife_XM3_07</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_08</Name>
      <Model>W_ME_Knife_XM3_08</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_KNIFE_VARMOD_XM3_09</Name>
      <Model>W_ME_Knife_XM3_09</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_TECPISTOL_CLIP_01</Name>
      <Model>W_PI_PistolSMG_M31_Mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_TECP_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="30" />
      <ReloadData ref="RELOAD_DEFAULT" />
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_AT_PI_FLSH_02_XM3</Name>
      <Model>w_at_pi_flsh_2_xm3</Model>
      <LocName>WCT_FLASH</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="6.000000" />
      <MainLightColor value="0xFFC9C9FF" />
      <MainLightRange value="30.000000" />
      <MainLightFalloffExponent value="32.000000" />
      <MainLightInnerAngle value="0.000000" />
      <MainLightOuterAngle value="20.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.100000" />
      <MainLightVolumeExponent value="70.000000" />
      <MainLightVolumeOuterColor value="0xFF050E3B" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="4.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="8.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_PISTOLXM3_CLIP_01</Name>
      <Model>W_PI_Pistol_XM3_Mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_PXM3_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="20" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_PISTOLXM3_CLIP_02</Name>
      <Model>W_PI_Pistol_XM3_Mag2</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_PXM3_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="20" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_COMBATMG_CLIP_03</Name>
      <Model>w_mg_combatmg_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCDCMG_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="30" />
      <ReloadData ref="RELOAD_DEFAULT" />
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
			<Name>COMPONENT_AT_GLOCK17_FLSH</Name>
			<Model>w_at_glock17_flsh</Model>
			<LocName>WCT_FLASH</LocName>
			<LocDesc>WCD_FLASH</LocDesc>
			<AttachBone>AAPFlsh</AttachBone>
			<WeaponAttachBone>WAPFlshLasr</WeaponAttachBone>
			<AccuracyModifier type="NULL"/>
			<DamageModifier type="NULL"/>
			<bShownOnWheel value="true"/>
			<CreateObject value="true"/>
			<HudDamage value="0"/>
			<HudSpeed value="0"/>
			<HudCapacity value="0"/>
			<HudAccuracy value="0"/>
			<HudRange value="0"/>
			<MainLightIntensity value="8.000000"/>
			<MainLightColor value="0xFFC9C9FF"/>
			<MainLightRange value="30.000000"/>
			<MainLightFalloffExponent value="32.000000"/>
			<MainLightInnerAngle value="0.000000"/>
			<MainLightOuterAngle value="20.000000"/>
			<MainLightCoronaIntensity value="3.000000"/>
			<MainLightCoronaSize value="0.200000"/>
			<MainLightVolumeIntensity value="0.300000"/>
			<MainLightVolumeSize value="0.100000"/>
			<MainLightVolumeExponent value="70.000000"/>
			<MainLightVolumeOuterColor value="0xFF050E3B"/>
			<MainLightShadowFadeDistance value="10.000000"/>
			<MainLightSpecularFadeDistance value="10.000000"/>
			<SecondaryLightIntensity value="5.000000"/>
			<SecondaryLightColor value="0xFFFFFFFF"/>
			<SecondaryLightRange value="8.000000"/>
			<SecondaryLightFalloffExponent value="64.000000"/>
			<SecondaryLightInnerAngle value="0.000000"/>
			<SecondaryLightOuterAngle value="40.000000"/>
			<SecondaryLightVolumeIntensity value="0.300000"/>
			<SecondaryLightVolumeSize value="0.400000"/>
			<SecondaryLightVolumeExponent value="24.000000"/>
			<SecondaryLightVolumeOuterColor value="0xFF000F85"/>
			<SecondaryLightFadeDistance value="10.000000"/>
			<fTargetDistalongAimCamera value="8.000000"/>
			<FlashLightBone>Gun_FLMuzzle</FlashLightBone>
			<FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
			<FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
		</Item>
    <Item type="CWeaponComponentClipInfo">
			<Name>COMPONENT_GLOCK17_CLIP_01</Name>
			<Model>w_pi_glock17_mag1</Model>
			<LocName>WCT_CLIP1</LocName>
			<LocDesc>WCD_P_CLIP1</LocDesc>
			<AttachBone>AAPClip</AttachBone>
			<WeaponAttachBone>WAPClip</WeaponAttachBone>
			<AccuracyModifier type="NULL"/>
			<DamageModifier type="NULL"/>
			<bShownOnWheel value="false"/>
			<CreateObject value="true"/>
			<HudDamage value="0"/>
			<HudSpeed value="0"/>
			<HudCapacity value="0"/>
			<HudAccuracy value="0"/>
			<HudRange value="0"/>
			<ClipSize value="14"/>
			<AmmoInfo/>
			<ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES"/>
		</Item>
    <Item type="CWeaponComponentClipInfo">
			<Name>COMPONENT_GLOCK17_CLIP_02</Name>
			<Model>w_pi_glock17_mag2</Model>
			<LocName>WCT_CLIP2</LocName>
			<LocDesc>WCD_P_CLIP2</LocDesc>
			<AttachBone>AAPClip</AttachBone>
			<WeaponAttachBone>WAPClip</WeaponAttachBone>
			<AccuracyModifier type="NULL"/>
			<DamageModifier type="NULL"/>
			<bShownOnWheel value="true"/>
			<CreateObject value="true"/>
			<HudDamage value="0"/>
			<HudSpeed value="0"/>
			<HudCapacity value="33"/>
			<HudAccuracy value="0"/>
			<HudRange value="0"/>
			<ClipSize value="17"/>
			<AmmoInfo/>
			<ReloadData ref="RELOAD_LARGE_WITH_EMPTIES"/>
		</Item>
  </Infos>
  <InfoBlobName>DLC - Lowrider</InfoBlobName>
</CWeaponComponentInfoBlob>

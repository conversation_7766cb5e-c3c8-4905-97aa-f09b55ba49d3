<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>polroamerp</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            1
            0
            134
            0
            0
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>61080_bl_polroamerp_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="58" />
    </Item>
    <Item>
      <modelName>emsroamerp</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            1
            0
            134
            0
            0
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>61080_bl_polroamerp_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="81" />
    </Item>
    <Item>
      <modelName>polroamerp2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            1
            0
            134
            0
            0
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="54" />
    </Item>
  </variationData>
</CVehicleModelInfoVariation>

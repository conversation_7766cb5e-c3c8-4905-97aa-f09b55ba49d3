AddEventHandler('blrp_vehicles:client:pbike:load', function(entity, event_data)
  if not entity or entity <= 0 or GetEntityType(entity) ~= 2 or not exports.blrp_core:me().hasGroup('LEO') then
    return
  end

  local network_id_bike = NetworkGetNetworkIdFromEntity(entity)

  local vehicle_target, distance = GetClosestVehicleDistance(5, `polbisonp`)

  if not vehicle_target or vehicle_target <= 0 or distance > 5 then
    exports.blrp_core:me().notify('No Bison found nearby')
    return
  end

  local network_id_truck = NetworkGetNetworkIdFromEntity(vehicle_target)

  if not IsVehicleSeatFree(vehicle_target, 3) or not IsVehicleSeatFree(vehicle_target, 4) then
    exports.blrp_core:me().notify('Unable to load bicycle')
    return
  end

  TriggerServerEvent('blrp_vehicles:server:pbike:load', network_id_bike, network_id_truck)
end)
